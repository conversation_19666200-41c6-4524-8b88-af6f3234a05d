{"comment": "Enhanced KNN calibration configurations for different scenarios", "knn_cosine_basic": {"prefix": "knn_cosine_basic", "dataset": "cub", "memory_size": 0, "shuffle": true, "init_cls": 100, "increment": 10, "model_name": "adam_adapter", "backbone_type": "pretrained_vit_b16_224_adapter", "device": ["0"], "seed": [1993], "resume": false, "shot": 5, "tuned_epoch": 40, "init_lr": 0.01, "batch_size": 48, "weight_decay": 0.0005, "min_lr": 0, "ffn_num": 64, "optimizer": "sgd", "calibration": true, "fecam": true, "knn_k": 5, "knn_distance_metric": "cosine", "knn_weight_decay": 0.1, "knn_adaptive_k": true, "knn_temperature": 16.0, "k_min": 3, "k_max": 21, "dynamic_k_method": "cosine_similarity", "cosine_temperature": 16.0}, "knn_euclidean_adaptive": {"prefix": "knn_euclidean_adaptive", "dataset": "cub", "memory_size": 0, "shuffle": true, "init_cls": 100, "increment": 10, "model_name": "adam_adapter", "backbone_type": "pretrained_vit_b16_224_adapter", "device": ["0"], "seed": [1993], "resume": false, "shot": 5, "tuned_epoch": 40, "init_lr": 0.01, "batch_size": 48, "weight_decay": 0.0005, "min_lr": 0, "ffn_num": 64, "optimizer": "sgd", "calibration": true, "fecam": true, "knn_k": 8, "knn_distance_metric": "euclidean", "knn_weight_decay": 0.2, "knn_adaptive_k": true, "knn_temperature": 10.0}, "knn_mahalanobis_sparse": {"prefix": "knn_ma<PERSON><PERSON><PERSON>_sparse", "dataset": "cub", "memory_size": 0, "shuffle": true, "init_cls": 100, "increment": 10, "model_name": "adam_adapter", "backbone_type": "pretrained_vit_b16_224_adapter", "device": ["0"], "seed": [1993], "resume": false, "shot": 5, "tuned_epoch": 40, "init_lr": 0.01, "batch_size": 48, "weight_decay": 0.0005, "min_lr": 0, "ffn_num": 64, "optimizer": "sgd", "calibration": true, "fecam": true, "knn_k": 3, "knn_distance_metric": "<PERSON><PERSON><PERSON><PERSON>", "knn_weight_decay": 0.3, "knn_adaptive_k": false, "knn_temperature": 20.0}, "ranpac_knn_enhanced": {"prefix": "ranpac_knn_enhanced", "dataset": "cub", "memory_size": 0, "shuffle": true, "init_cls": 100, "increment": 10, "model_name": "ranpac", "backbone_type": "pretrained_vit_b16_224_adapter", "device": ["0"], "seed": [1993], "resume": false, "shot": 5, "use_simplecil": false, "tuned_epoch": 1, "init_lr": 0.01, "batch_size": 48, "weight_decay": 0.0005, "min_lr": 0, "ffn_num": 64, "optimizer": "sgd", "use_RP": true, "M": 10000, "fecam": false, "calibration": true, "knn_k": 7, "knn_distance_metric": "cosine", "knn_weight_decay": 0.15, "knn_adaptive_k": true, "knn_temperature": 16.0, "k_min": 3, "k_max": 21, "dynamic_k_method": "cosine_similarity", "cosine_temperature": 16.0}, "dynamic_k_cosine_test": {"prefix": "dynamic_k_cosine_test", "dataset": "cub", "memory_size": 0, "shuffle": true, "init_cls": 100, "increment": 10, "model_name": "adam_adapter", "backbone_type": "pretrained_vit_b16_224_adapter", "device": ["0"], "seed": [1993], "resume": false, "shot": 5, "tuned_epoch": 40, "init_lr": 0.01, "batch_size": 48, "weight_decay": 0.0005, "min_lr": 0, "ffn_num": 64, "optimizer": "sgd", "calibration": true, "fecam": true, "knn_k": 10, "knn_distance_metric": "cosine", "knn_weight_decay": 0.2, "knn_adaptive_k": true, "knn_temperature": 16.0, "k_min": 2, "k_max": 25, "dynamic_k_method": "cosine_similarity", "cosine_temperature": 16.0}}