2025-07-14 22:06:21,785 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/ranpac_euclideanjson
2025-07-14 22:06:21,789 [trainer.py] => prefix: reproduce
2025-07-14 22:06:21,789 [trainer.py] => dataset: cub
2025-07-14 22:06:21,789 [trainer.py] => memory_size: 0
2025-07-14 22:06:21,790 [trainer.py] => shuffle: True
2025-07-14 22:06:21,790 [trainer.py] => init_cls: 100
2025-07-14 22:06:21,790 [trainer.py] => increment: 10
2025-07-14 22:06:21,790 [trainer.py] => model_name: ranpac
2025-07-14 22:06:21,790 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 22:06:21,790 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 22:06:21,790 [trainer.py] => seed: 1993
2025-07-14 22:06:21,791 [trainer.py] => resume: False
2025-07-14 22:06:21,791 [trainer.py] => shot: 5
2025-07-14 22:06:21,791 [trainer.py] => use_simplecil: False
2025-07-14 22:06:21,791 [trainer.py] => tuned_epoch: 40
2025-07-14 22:06:21,791 [trainer.py] => init_lr: 0.01
2025-07-14 22:06:21,791 [trainer.py] => batch_size: 48
2025-07-14 22:06:21,791 [trainer.py] => weight_decay: 0.0005
2025-07-14 22:06:21,792 [trainer.py] => min_lr: 0
2025-07-14 22:06:21,792 [trainer.py] => ffn_num: 64
2025-07-14 22:06:21,792 [trainer.py] => optimizer: sgd
2025-07-14 22:06:21,792 [trainer.py] => use_RP: True
2025-07-14 22:06:21,792 [trainer.py] => M: 10000
2025-07-14 22:06:21,792 [trainer.py] => fecam: False
2025-07-14 22:06:21,792 [trainer.py] => calibration: True
2025-07-14 22:06:21,792 [trainer.py] => knn_k: 5
2025-07-14 22:06:21,793 [trainer.py] => knn_distance_metric: euclidean
2025-07-14 22:06:21,793 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 22:06:21,793 [trainer.py] => knn_adaptive_k: True
2025-07-14 22:06:21,793 [trainer.py] => knn_temperature: 16.0
2025-07-14 22:06:21,793 [trainer.py] => k_min: 3
2025-07-14 22:06:21,793 [trainer.py] => k_max: 21
2025-07-14 22:06:21,793 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-14 22:06:21,794 [trainer.py] => cosine_temperature: 16.0
2025-07-14 22:06:21,910 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
This is for the BaseNet initialization.
I'm using ViT with adapters.
_IncompatibleKeys(missing_keys=['blocks.0.adaptmlp.down_proj.weight', 'blocks.0.adaptmlp.down_proj.bias', 'blocks.0.adaptmlp.up_proj.weight', 'blocks.0.adaptmlp.up_proj.bias', 'blocks.1.adaptmlp.down_proj.weight', 'blocks.1.adaptmlp.down_proj.bias', 'blocks.1.adaptmlp.up_proj.weight', 'blocks.1.adaptmlp.up_proj.bias', 'blocks.2.adaptmlp.down_proj.weight', 'blocks.2.adaptmlp.down_proj.bias', 'blocks.2.adaptmlp.up_proj.weight', 'blocks.2.adaptmlp.up_proj.bias', 'blocks.3.adaptmlp.down_proj.weight', 'blocks.3.adaptmlp.down_proj.bias', 'blocks.3.adaptmlp.up_proj.weight', 'blocks.3.adaptmlp.up_proj.bias', 'blocks.4.adaptmlp.down_proj.weight', 'blocks.4.adaptmlp.down_proj.bias', 'blocks.4.adaptmlp.up_proj.weight', 'blocks.4.adaptmlp.up_proj.bias', 'blocks.5.adaptmlp.down_proj.weight', 'blocks.5.adaptmlp.down_proj.bias', 'blocks.5.adaptmlp.up_proj.weight', 'blocks.5.adaptmlp.up_proj.bias', 'blocks.6.adaptmlp.down_proj.weight', 'blocks.6.adaptmlp.down_proj.bias', 'blocks.6.adaptmlp.up_proj.weight', 'blocks.6.adaptmlp.up_proj.bias', 'blocks.7.adaptmlp.down_proj.weight', 'blocks.7.adaptmlp.down_proj.bias', 'blocks.7.adaptmlp.up_proj.weight', 'blocks.7.adaptmlp.up_proj.bias', 'blocks.8.adaptmlp.down_proj.weight', 'blocks.8.adaptmlp.down_proj.bias', 'blocks.8.adaptmlp.up_proj.weight', 'blocks.8.adaptmlp.up_proj.bias', 'blocks.9.adaptmlp.down_proj.weight', 'blocks.9.adaptmlp.down_proj.bias', 'blocks.9.adaptmlp.up_proj.weight', 'blocks.9.adaptmlp.up_proj.bias', 'blocks.10.adaptmlp.down_proj.weight', 'blocks.10.adaptmlp.down_proj.bias', 'blocks.10.adaptmlp.up_proj.weight', 'blocks.10.adaptmlp.up_proj.bias', 'blocks.11.adaptmlp.down_proj.weight', 'blocks.11.adaptmlp.down_proj.bias', 'blocks.11.adaptmlp.up_proj.weight', 'blocks.11.adaptmlp.up_proj.bias'], unexpected_keys=[])
After BaseNet initialization.
2025-07-14 22:06:48,866 [trainer.py] => All params: 86988288
2025-07-14 22:06:48,867 [trainer.py] => Trainable params: 1189632
2025-07-14 22:07:04,426 [ranpac.py] => Learning on 0-100
87,065,089 total parameters.
1,266,433 training parameters.
backbone.blocks.0.adaptmlp.down_proj.weight 49152
backbone.blocks.0.adaptmlp.down_proj.bias 64
backbone.blocks.0.adaptmlp.up_proj.weight 49152
backbone.blocks.0.adaptmlp.up_proj.bias 768
backbone.blocks.1.adaptmlp.down_proj.weight 49152
backbone.blocks.1.adaptmlp.down_proj.bias 64
backbone.blocks.1.adaptmlp.up_proj.weight 49152
backbone.blocks.1.adaptmlp.up_proj.bias 768
backbone.blocks.2.adaptmlp.down_proj.weight 49152
backbone.blocks.2.adaptmlp.down_proj.bias 64
backbone.blocks.2.adaptmlp.up_proj.weight 49152
backbone.blocks.2.adaptmlp.up_proj.bias 768
backbone.blocks.3.adaptmlp.down_proj.weight 49152
backbone.blocks.3.adaptmlp.down_proj.bias 64
backbone.blocks.3.adaptmlp.up_proj.weight 49152
backbone.blocks.3.adaptmlp.up_proj.bias 768
backbone.blocks.4.adaptmlp.down_proj.weight 49152
backbone.blocks.4.adaptmlp.down_proj.bias 64
backbone.blocks.4.adaptmlp.up_proj.weight 49152
backbone.blocks.4.adaptmlp.up_proj.bias 768
backbone.blocks.5.adaptmlp.down_proj.weight 49152
backbone.blocks.5.adaptmlp.down_proj.bias 64
backbone.blocks.5.adaptmlp.up_proj.weight 49152
backbone.blocks.5.adaptmlp.up_proj.bias 768
backbone.blocks.6.adaptmlp.down_proj.weight 49152
backbone.blocks.6.adaptmlp.down_proj.bias 64
backbone.blocks.6.adaptmlp.up_proj.weight 49152
backbone.blocks.6.adaptmlp.up_proj.bias 768
backbone.blocks.7.adaptmlp.down_proj.weight 49152
backbone.blocks.7.adaptmlp.down_proj.bias 64
backbone.blocks.7.adaptmlp.up_proj.weight 49152
backbone.blocks.7.adaptmlp.up_proj.bias 768
backbone.blocks.8.adaptmlp.down_proj.weight 49152
backbone.blocks.8.adaptmlp.down_proj.bias 64
backbone.blocks.8.adaptmlp.up_proj.weight 49152
backbone.blocks.8.adaptmlp.up_proj.bias 768
backbone.blocks.9.adaptmlp.down_proj.weight 49152
backbone.blocks.9.adaptmlp.down_proj.bias 64
backbone.blocks.9.adaptmlp.up_proj.weight 49152
backbone.blocks.9.adaptmlp.up_proj.bias 768
backbone.blocks.10.adaptmlp.down_proj.weight 49152
backbone.blocks.10.adaptmlp.down_proj.bias 64
backbone.blocks.10.adaptmlp.up_proj.weight 49152
backbone.blocks.10.adaptmlp.up_proj.bias 768
backbone.blocks.11.adaptmlp.down_proj.weight 49152
backbone.blocks.11.adaptmlp.down_proj.bias 64
backbone.blocks.11.adaptmlp.up_proj.weight 49152
backbone.blocks.11.adaptmlp.up_proj.bias 768
fc.weight 76800
fc.sigma 1

  0%|          | 0/40 [00:00<?, ?it/s]
Task 0, Epoch 1/40 => Loss 4.586, Train_accy 4.84, Test_accy 14.84:   0%|          | 0/40 [00:59<?, ?it/s]
Task 0, Epoch 1/40 => Loss 4.586, Train_accy 4.84, Test_accy 14.84:   2%|▎         | 1/40 [00:59<38:46, 59.65s/it]
Task 0, Epoch 2/40 => Loss 4.519, Train_accy 23.96, Test_accy 43.64:   2%|▎         | 1/40 [01:27<38:46, 59.65s/it]
Task 0, Epoch 2/40 => Loss 4.519, Train_accy 23.96, Test_accy 43.64:   5%|▌         | 2/40 [01:27<26:06, 41.23s/it]
Task 0, Epoch 3/40 => Loss 4.366, Train_accy 45.91, Test_accy 63.67:   5%|▌         | 2/40 [02:01<26:06, 41.23s/it]
Task 0, Epoch 3/40 => Loss 4.366, Train_accy 45.91, Test_accy 63.67:   8%|▊         | 3/40 [02:01<23:23, 37.93s/it]
Task 0, Epoch 4/40 => Loss 3.998, Train_accy 61.86, Test_accy 74.80:   8%|▊         | 3/40 [02:21<23:23, 37.93s/it]
Task 0, Epoch 4/40 => Loss 3.998, Train_accy 61.86, Test_accy 74.80:  10%|█         | 4/40 [02:21<18:17, 30.49s/it]
Task 0, Epoch 5/40 => Loss 3.248, Train_accy 71.81, Test_accy 81.01:  10%|█         | 4/40 [02:40<18:17, 30.49s/it]
Task 0, Epoch 5/40 => Loss 3.248, Train_accy 71.81, Test_accy 81.01:  12%|█▎        | 5/40 [02:40<15:33, 26.66s/it]
Task 0, Epoch 6/40 => Loss 2.086, Train_accy 77.68, Test_accy 86.14:  12%|█▎        | 5/40 [03:10<15:33, 26.66s/it]
Task 0, Epoch 6/40 => Loss 2.086, Train_accy 77.68, Test_accy 86.14:  15%|█▌        | 6/40 [03:10<15:39, 27.64s/it]
Task 0, Epoch 7/40 => Loss 1.318, Train_accy 82.12, Test_accy 88.25:  15%|█▌        | 6/40 [03:41<15:39, 27.64s/it]
Task 0, Epoch 7/40 => Loss 1.318, Train_accy 82.12, Test_accy 88.25:  18%|█▊        | 7/40 [03:41<15:49, 28.79s/it]
Task 0, Epoch 8/40 => Loss 0.986, Train_accy 84.75, Test_accy 89.12:  18%|█▊        | 7/40 [04:01<15:49, 28.79s/it]
Task 0, Epoch 8/40 => Loss 0.986, Train_accy 84.75, Test_accy 89.12:  20%|██        | 8/40 [04:01<13:46, 25.82s/it]
Task 0, Epoch 9/40 => Loss 0.848, Train_accy 85.39, Test_accy 89.95:  20%|██        | 8/40 [04:19<13:46, 25.82s/it]
Task 0, Epoch 9/40 => Loss 0.848, Train_accy 85.39, Test_accy 89.95:  22%|██▎       | 9/40 [04:19<12:10, 23.57s/it]
Task 0, Epoch 10/40 => Loss 0.739, Train_accy 87.12, Test_accy 89.84:  22%|██▎       | 9/40 [04:38<12:10, 23.57s/it]
Task 0, Epoch 10/40 => Loss 0.739, Train_accy 87.12, Test_accy 89.84:  25%|██▌       | 10/40 [04:38<11:03, 22.13s/it]
Task 0, Epoch 11/40 => Loss 0.668, Train_accy 87.65, Test_accy 90.61:  25%|██▌       | 10/40 [05:00<11:03, 22.13s/it]
Task 0, Epoch 11/40 => Loss 0.668, Train_accy 87.65, Test_accy 90.61:  28%|██▊       | 11/40 [05:00<10:40, 22.08s/it]
Task 0, Epoch 12/40 => Loss 0.636, Train_accy 87.45, Test_accy 91.61:  28%|██▊       | 11/40 [05:35<10:40, 22.08s/it]
Task 0, Epoch 12/40 => Loss 0.636, Train_accy 87.45, Test_accy 91.61:  30%|███       | 12/40 [05:35<12:10, 26.09s/it]
Task 0, Epoch 13/40 => Loss 0.620, Train_accy 88.02, Test_accy 91.47:  30%|███       | 12/40 [05:59<12:10, 26.09s/it]
Task 0, Epoch 13/40 => Loss 0.620, Train_accy 88.02, Test_accy 91.47:  32%|███▎      | 13/40 [05:59<11:21, 25.23s/it]
Task 0, Epoch 14/40 => Loss 0.569, Train_accy 89.52, Test_accy 91.68:  32%|███▎      | 13/40 [06:22<11:21, 25.23s/it]
Task 0, Epoch 14/40 => Loss 0.569, Train_accy 89.52, Test_accy 91.68:  35%|███▌      | 14/40 [06:22<10:45, 24.83s/it]
Task 0, Epoch 15/40 => Loss 0.540, Train_accy 89.79, Test_accy 91.75:  35%|███▌      | 14/40 [06:46<10:45, 24.83s/it]
Task 0, Epoch 15/40 => Loss 0.540, Train_accy 89.79, Test_accy 91.75:  38%|███▊      | 15/40 [06:46<10:13, 24.56s/it]
Task 0, Epoch 16/40 => Loss 0.547, Train_accy 89.09, Test_accy 91.96:  38%|███▊      | 15/40 [07:06<10:13, 24.56s/it]
Task 0, Epoch 16/40 => Loss 0.547, Train_accy 89.09, Test_accy 91.96:  40%|████      | 16/40 [07:06<09:10, 22.93s/it]
Task 0, Epoch 17/40 => Loss 0.530, Train_accy 89.22, Test_accy 91.96:  40%|████      | 16/40 [07:24<09:10, 22.93s/it]
Task 0, Epoch 17/40 => Loss 0.530, Train_accy 89.22, Test_accy 91.96:  42%|████▎     | 17/40 [07:24<08:19, 21.70s/it]
Task 0, Epoch 18/40 => Loss 0.488, Train_accy 90.36, Test_accy 92.27:  42%|████▎     | 17/40 [07:45<08:19, 21.70s/it]
Task 0, Epoch 18/40 => Loss 0.488, Train_accy 90.36, Test_accy 92.27:  45%|████▌     | 18/40 [07:45<07:50, 21.40s/it]
Task 0, Epoch 19/40 => Loss 0.531, Train_accy 89.06, Test_accy 92.20:  45%|████▌     | 18/40 [08:09<07:50, 21.40s/it]
Task 0, Epoch 19/40 => Loss 0.531, Train_accy 89.06, Test_accy 92.20:  48%|████▊     | 19/40 [08:09<07:47, 22.25s/it]
Task 0, Epoch 20/40 => Loss 0.486, Train_accy 90.06, Test_accy 92.03:  48%|████▊     | 19/40 [08:39<07:47, 22.25s/it]
Task 0, Epoch 20/40 => Loss 0.486, Train_accy 90.06, Test_accy 92.03:  50%|█████     | 20/40 [08:39<08:12, 24.62s/it]
Task 0, Epoch 21/40 => Loss 0.443, Train_accy 91.79, Test_accy 92.10:  50%|█████     | 20/40 [09:06<08:12, 24.62s/it]
Task 0, Epoch 21/40 => Loss 0.443, Train_accy 91.79, Test_accy 92.10:  52%|█████▎    | 21/40 [09:06<07:57, 25.14s/it]
Task 0, Epoch 22/40 => Loss 0.431, Train_accy 91.62, Test_accy 92.44:  52%|█████▎    | 21/40 [09:42<07:57, 25.14s/it]
Task 0, Epoch 22/40 => Loss 0.431, Train_accy 91.62, Test_accy 92.44:  55%|█████▌    | 22/40 [09:42<08:29, 28.33s/it]
Task 0, Epoch 23/40 => Loss 0.443, Train_accy 91.26, Test_accy 92.37:  55%|█████▌    | 22/40 [10:06<08:29, 28.33s/it]
Task 0, Epoch 23/40 => Loss 0.443, Train_accy 91.26, Test_accy 92.37:  57%|█████▊    | 23/40 [10:06<07:39, 27.04s/it]
Task 0, Epoch 24/40 => Loss 0.442, Train_accy 91.59, Test_accy 92.37:  57%|█████▊    | 23/40 [10:24<07:39, 27.04s/it]
Task 0, Epoch 24/40 => Loss 0.442, Train_accy 91.59, Test_accy 92.37:  60%|██████    | 24/40 [10:24<06:33, 24.58s/it]
Task 0, Epoch 25/40 => Loss 0.433, Train_accy 91.12, Test_accy 92.44:  60%|██████    | 24/40 [10:46<06:33, 24.58s/it]
Task 0, Epoch 25/40 => Loss 0.433, Train_accy 91.12, Test_accy 92.44:  62%|██████▎   | 25/40 [10:46<05:53, 23.58s/it]
Task 0, Epoch 26/40 => Loss 0.421, Train_accy 91.83, Test_accy 92.37:  62%|██████▎   | 25/40 [11:11<05:53, 23.58s/it]
Task 0, Epoch 26/40 => Loss 0.421, Train_accy 91.83, Test_accy 92.37:  65%|██████▌   | 26/40 [11:11<05:36, 24.04s/it]
Task 0, Epoch 27/40 => Loss 0.417, Train_accy 92.46, Test_accy 92.17:  65%|██████▌   | 26/40 [11:43<05:36, 24.04s/it]
Task 0, Epoch 27/40 => Loss 0.417, Train_accy 92.46, Test_accy 92.17:  68%|██████▊   | 27/40 [11:43<05:43, 26.41s/it]
Task 0, Epoch 28/40 => Loss 0.409, Train_accy 91.52, Test_accy 92.34:  68%|██████▊   | 27/40 [12:05<05:43, 26.41s/it]
Task 0, Epoch 28/40 => Loss 0.409, Train_accy 91.52, Test_accy 92.34:  70%|███████   | 28/40 [12:05<05:00, 25.08s/it]
Task 0, Epoch 29/40 => Loss 0.397, Train_accy 91.89, Test_accy 92.31:  70%|███████   | 28/40 [12:40<05:00, 25.08s/it]
Task 0, Epoch 29/40 => Loss 0.397, Train_accy 91.89, Test_accy 92.31:  72%|███████▎  | 29/40 [12:40<05:10, 28.19s/it]
Task 0, Epoch 30/40 => Loss 0.417, Train_accy 91.79, Test_accy 92.34:  72%|███████▎  | 29/40 [13:14<05:10, 28.19s/it]
Task 0, Epoch 30/40 => Loss 0.417, Train_accy 91.79, Test_accy 92.34:  75%|███████▌  | 30/40 [13:14<04:57, 29.75s/it]
Task 0, Epoch 31/40 => Loss 0.400, Train_accy 92.43, Test_accy 92.34:  75%|███████▌  | 30/40 [13:37<04:57, 29.75s/it]
Task 0, Epoch 31/40 => Loss 0.400, Train_accy 92.43, Test_accy 92.34:  78%|███████▊  | 31/40 [13:37<04:10, 27.86s/it]
Task 0, Epoch 32/40 => Loss 0.408, Train_accy 92.26, Test_accy 92.41:  78%|███████▊  | 31/40 [13:56<04:10, 27.86s/it]
Task 0, Epoch 32/40 => Loss 0.408, Train_accy 92.26, Test_accy 92.41:  80%|████████  | 32/40 [13:56<03:22, 25.32s/it]
Task 0, Epoch 33/40 => Loss 0.405, Train_accy 92.43, Test_accy 92.44:  80%|████████  | 32/40 [14:24<03:22, 25.32s/it]
Task 0, Epoch 33/40 => Loss 0.405, Train_accy 92.43, Test_accy 92.44:  82%|████████▎ | 33/40 [14:24<03:02, 26.03s/it]
Task 0, Epoch 34/40 => Loss 0.408, Train_accy 92.09, Test_accy 92.48:  82%|████████▎ | 33/40 [14:58<03:02, 26.03s/it]
Task 0, Epoch 34/40 => Loss 0.408, Train_accy 92.09, Test_accy 92.48:  85%|████████▌ | 34/40 [14:58<02:49, 28.33s/it]
Task 0, Epoch 35/40 => Loss 0.389, Train_accy 92.46, Test_accy 92.55:  85%|████████▌ | 34/40 [15:18<02:49, 28.33s/it]
Task 0, Epoch 35/40 => Loss 0.389, Train_accy 92.46, Test_accy 92.55:  88%|████████▊ | 35/40 [15:18<02:09, 25.88s/it]
Task 0, Epoch 36/40 => Loss 0.406, Train_accy 91.89, Test_accy 92.48:  88%|████████▊ | 35/40 [15:43<02:09, 25.88s/it]
Task 0, Epoch 36/40 => Loss 0.406, Train_accy 91.89, Test_accy 92.48:  90%|█████████ | 36/40 [15:43<01:42, 25.75s/it]
Task 0, Epoch 37/40 => Loss 0.403, Train_accy 91.89, Test_accy 92.44:  90%|█████████ | 36/40 [16:12<01:42, 25.75s/it]
Task 0, Epoch 37/40 => Loss 0.403, Train_accy 91.89, Test_accy 92.44:  92%|█████████▎| 37/40 [16:12<01:19, 26.54s/it]
Task 0, Epoch 38/40 => Loss 0.411, Train_accy 92.09, Test_accy 92.48:  92%|█████████▎| 37/40 [16:44<01:19, 26.54s/it]
Task 0, Epoch 38/40 => Loss 0.411, Train_accy 92.09, Test_accy 92.48:  95%|█████████▌| 38/40 [16:44<00:56, 28.23s/it]
Task 0, Epoch 39/40 => Loss 0.394, Train_accy 92.09, Test_accy 92.48:  95%|█████████▌| 38/40 [17:20<00:56, 28.23s/it]
Task 0, Epoch 39/40 => Loss 0.394, Train_accy 92.09, Test_accy 92.48:  98%|█████████▊| 39/40 [17:20<00:30, 30.57s/it]
Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48:  98%|█████████▊| 39/40 [17:54<00:30, 30.57s/it]
Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48: 100%|██████████| 40/40 [17:54<00:00, 31.50s/it]
Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48: 100%|██████████| 40/40 [17:54<00:00, 26.85s/it]
2025-07-14 22:24:58,654 [ranpac.py] => Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48
selected lambda =  1000000.0
2025-07-14 22:25:28,572 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-14 22:25:29,029 [ranpac.py] => [Dynamic-K] Base similarity range: [-0.0264, 0.8900]
2025-07-14 22:25:29,029 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-14 22:25:29,029 [trainer.py] => No NME accuracy.
2025-07-14 22:25:29,029 [trainer.py] => CNN: {'total': 92.44, '00-99': 92.44, 'old': 0, 'new': 92.44}
2025-07-14 22:25:29,029 [trainer.py] => CNN HM: [0.0]
2025-07-14 22:25:29,029 [trainer.py] => CNN top1 curve: [92.44]
Average Accuracy (CNN): 92.44
2025-07-14 22:25:29,029 [trainer.py] => Average Accuracy (CNN): 92.44 

2025-07-14 22:25:29,030 [trainer.py] => All params: 87988289
2025-07-14 22:25:29,030 [trainer.py] => Trainable params: 1189633
2025-07-14 22:25:29,039 [ranpac.py] => Learning on 100-110
2025-07-14 22:25:32,003 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:25:32,036 [ranpac.py] => [Dynamic-K] Computed K values: [13, 14, 7, 14, 7, 7, 13, 10, 12, 8]
2025-07-14 22:25:32,036 [ranpac.py] => [KNN] task 1, dynamic K values: [13, 14, 7, 14, 7, 7, 13, 10, 12, 8]
2025-07-14 22:25:32,200 [ranpac.py] => [KNN] task 1, weight sparsity: 0.908, distance_metric: euclidean
/home/<USER>/workdir/FSCIL-Calibration-main/models/ranpac.py:137: RuntimeWarning: covariance is not positive-semidefinite.
  xx  = np.random.multivariate_normal(self.proto_list[class_index], self.cov_mats[class_index], 800)
2025-07-14 22:25:54,211 [trainer.py] => No NME accuracy.
2025-07-14 22:25:54,211 [trainer.py] => CNN: {'total': 91.54, '00-99': 91.79, '100-109': 89.19, 'old': 91.79, 'new': 89.19}
2025-07-14 22:25:54,211 [trainer.py] => CNN HM: [0.0, 90.471]
2025-07-14 22:25:54,211 [trainer.py] => CNN top1 curve: [92.44, 91.54]
Average Accuracy (CNN): 91.99000000000001
2025-07-14 22:25:54,211 [trainer.py] => Average Accuracy (CNN): 91.99000000000001 

2025-07-14 22:25:54,212 [trainer.py] => All params: 88088289
2025-07-14 22:25:54,213 [trainer.py] => Trainable params: 2289633
2025-07-14 22:25:54,225 [ranpac.py] => Learning on 110-120
2025-07-14 22:25:57,211 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:25:57,213 [ranpac.py] => [Dynamic-K] Computed K values: [7, 12, 9, 7, 13, 13, 13, 5, 13, 10]
2025-07-14 22:25:57,213 [ranpac.py] => [KNN] task 2, dynamic K values: [7, 12, 9, 7, 13, 13, 13, 5, 13, 10]
2025-07-14 22:25:57,213 [ranpac.py] => [KNN] task 2, weight sparsity: 0.910, distance_metric: euclidean
2025-07-14 22:26:13,150 [trainer.py] => No NME accuracy.
2025-07-14 22:26:13,150 [trainer.py] => CNN: {'total': 90.53, '00-99': 91.85, '100-109': 89.86, '110-119': 77.82, 'old': 91.67, 'new': 77.82}
2025-07-14 22:26:13,151 [trainer.py] => CNN HM: [0.0, 90.471, 84.179]
2025-07-14 22:26:13,151 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53]
Average Accuracy (CNN): 91.50333333333333
2025-07-14 22:26:13,151 [trainer.py] => Average Accuracy (CNN): 91.50333333333333 

2025-07-14 22:26:13,152 [trainer.py] => All params: 88188289
2025-07-14 22:26:13,153 [trainer.py] => Trainable params: 2389633
2025-07-14 22:26:13,170 [ranpac.py] => Learning on 120-130
2025-07-14 22:26:16,242 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:26:16,245 [ranpac.py] => [Dynamic-K] Computed K values: [8, 8, 10, 9, 8, 4, 5, 9, 4, 3]
2025-07-14 22:26:16,246 [ranpac.py] => [KNN] task 3, dynamic K values: [8, 8, 10, 9, 8, 4, 5, 9, 4, 3]
2025-07-14 22:26:16,247 [ranpac.py] => [KNN] task 3, weight sparsity: 0.945, distance_metric: euclidean
2025-07-14 22:26:31,822 [trainer.py] => No NME accuracy.
2025-07-14 22:26:31,823 [trainer.py] => CNN: {'total': 89.16, '00-99': 91.09, '100-109': 91.22, '110-119': 79.23, '120-129': 77.59, 'old': 90.13, 'new': 77.59}
2025-07-14 22:26:31,824 [trainer.py] => CNN HM: [0.0, 90.471, 84.179, 83.391]
2025-07-14 22:26:31,824 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53, 89.16]
Average Accuracy (CNN): 90.91749999999999
2025-07-14 22:26:31,825 [trainer.py] => Average Accuracy (CNN): 90.91749999999999 

2025-07-14 22:26:31,826 [trainer.py] => All params: 88288289
2025-07-14 22:26:31,827 [trainer.py] => Trainable params: 2489633
2025-07-14 22:26:31,842 [ranpac.py] => Learning on 130-140
2025-07-14 22:26:35,190 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:26:35,193 [ranpac.py] => [Dynamic-K] Computed K values: [13, 9, 15, 9, 12, 5, 7, 9, 10, 6]
2025-07-14 22:26:35,193 [ranpac.py] => [KNN] task 4, dynamic K values: [13, 9, 15, 9, 12, 5, 7, 9, 10, 6]
2025-07-14 22:26:35,194 [ranpac.py] => [KNN] task 4, weight sparsity: 0.915, distance_metric: euclidean
2025-07-14 22:26:52,772 [trainer.py] => No NME accuracy.
2025-07-14 22:26:52,773 [trainer.py] => CNN: {'total': 88.73, '00-99': 90.64, '100-109': 91.55, '110-119': 79.93, '120-129': 76.55, '130-139': 87.59, 'old': 88.81, 'new': 87.59}
2025-07-14 22:26:52,773 [trainer.py] => CNN HM: [0.0, 90.471, 84.179, 83.391, 88.196]
2025-07-14 22:26:52,773 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53, 89.16, 88.73]
Average Accuracy (CNN): 90.47999999999999
2025-07-14 22:26:52,774 [trainer.py] => Average Accuracy (CNN): 90.47999999999999 

2025-07-14 22:26:52,775 [trainer.py] => All params: 88388289
2025-07-14 22:26:52,777 [trainer.py] => Trainable params: 2589633
2025-07-14 22:26:52,790 [ranpac.py] => Learning on 140-150
2025-07-14 22:26:56,497 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:26:56,499 [ranpac.py] => [Dynamic-K] Computed K values: [9, 19, 10, 6, 7, 13, 15, 6, 3, 12]
2025-07-14 22:26:56,500 [ranpac.py] => [KNN] task 5, dynamic K values: [9, 19, 10, 6, 7, 13, 15, 6, 3, 12]
2025-07-14 22:26:56,500 [ranpac.py] => [KNN] task 5, weight sparsity: 0.912, distance_metric: euclidean
2025-07-14 22:27:26,543 [trainer.py] => No NME accuracy.
2025-07-14 22:27:26,544 [trainer.py] => CNN: {'total': 87.49, '00-99': 90.19, '100-109': 92.23, '110-119': 77.11, '120-129': 76.9, '130-139': 87.93, '140-149': 75.54, 'old': 88.31, 'new': 75.54}
2025-07-14 22:27:26,544 [trainer.py] => CNN HM: [0.0, 90.471, 84.179, 83.391, 88.196, 81.427]
2025-07-14 22:27:26,544 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53, 89.16, 88.73, 87.49]
Average Accuracy (CNN): 89.98166666666667
2025-07-14 22:27:26,544 [trainer.py] => Average Accuracy (CNN): 89.98166666666667 

2025-07-14 22:27:26,545 [trainer.py] => All params: 88488289
2025-07-14 22:27:26,545 [trainer.py] => Trainable params: 2689633
2025-07-14 22:27:26,559 [ranpac.py] => Learning on 150-160
2025-07-14 22:27:28,988 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:27:28,990 [ranpac.py] => [Dynamic-K] Computed K values: [11, 10, 8, 5, 8, 6, 8, 5, 17, 11]
2025-07-14 22:27:28,990 [ranpac.py] => [KNN] task 6, dynamic K values: [11, 10, 8, 5, 8, 6, 8, 5, 17, 11]
2025-07-14 22:27:28,991 [ranpac.py] => [KNN] task 6, weight sparsity: 0.924, distance_metric: euclidean
2025-07-14 22:27:45,365 [trainer.py] => No NME accuracy.
2025-07-14 22:27:45,365 [trainer.py] => CNN: {'total': 87.17, '00-99': 90.16, '100-109': 90.54, '110-119': 78.87, '120-129': 76.55, '130-139': 87.59, '140-149': 74.82, '150-159': 84.25, 'old': 87.37, 'new': 84.25}
2025-07-14 22:27:45,365 [trainer.py] => CNN HM: [0.0, 90.471, 84.179, 83.391, 88.196, 81.427, 85.782]
2025-07-14 22:27:45,365 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53, 89.16, 88.73, 87.49, 87.17]
Average Accuracy (CNN): 89.58
2025-07-14 22:27:45,365 [trainer.py] => Average Accuracy (CNN): 89.58 

2025-07-14 22:27:45,366 [trainer.py] => All params: 88588289
2025-07-14 22:27:45,367 [trainer.py] => Trainable params: 2789633
2025-07-14 22:27:45,382 [ranpac.py] => Learning on 160-170
2025-07-14 22:27:49,128 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:27:49,129 [ranpac.py] => [Dynamic-K] Computed K values: [18, 13, 10, 7, 12, 10, 6, 6, 9, 11]
2025-07-14 22:27:49,129 [ranpac.py] => [KNN] task 7, dynamic K values: [18, 13, 10, 7, 12, 10, 6, 6, 9, 11]
2025-07-14 22:27:49,130 [ranpac.py] => [KNN] task 7, weight sparsity: 0.911, distance_metric: euclidean
2025-07-14 22:28:04,535 [trainer.py] => No NME accuracy.
2025-07-14 22:28:04,535 [trainer.py] => CNN: {'total': 86.48, '00-99': 89.81, '100-109': 90.88, '110-119': 77.46, '120-129': 76.21, '130-139': 87.24, '140-149': 69.42, '150-159': 85.27, '160-169': 84.9, 'old': 86.59, 'new': 84.9}
2025-07-14 22:28:04,536 [trainer.py] => CNN HM: [0.0, 90.471, 84.179, 83.391, 88.196, 81.427, 85.782, 85.737]
2025-07-14 22:28:04,536 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53, 89.16, 88.73, 87.49, 87.17, 86.48]
Average Accuracy (CNN): 89.1925
2025-07-14 22:28:04,536 [trainer.py] => Average Accuracy (CNN): 89.1925 

2025-07-14 22:28:04,538 [trainer.py] => All params: 88688289
2025-07-14 22:28:04,539 [trainer.py] => Trainable params: 2889633
2025-07-14 22:28:04,553 [ranpac.py] => Learning on 170-180
2025-07-14 22:28:07,227 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:28:07,229 [ranpac.py] => [Dynamic-K] Computed K values: [14, 5, 11, 6, 10, 6, 7, 7, 13, 4]
2025-07-14 22:28:07,229 [ranpac.py] => [KNN] task 8, dynamic K values: [14, 5, 11, 6, 10, 6, 7, 7, 13, 4]
2025-07-14 22:28:07,230 [ranpac.py] => [KNN] task 8, weight sparsity: 0.928, distance_metric: euclidean
2025-07-14 22:28:22,785 [trainer.py] => No NME accuracy.
2025-07-14 22:28:22,785 [trainer.py] => CNN: {'total': 86.13, '00-99': 89.39, '100-109': 90.54, '110-119': 77.11, '120-129': 75.17, '130-139': 86.9, '140-149': 71.22, '150-159': 84.93, '160-169': 84.56, '170-179': 85.23, 'old': 86.18, 'new': 85.23}
2025-07-14 22:28:22,785 [trainer.py] => CNN HM: [0.0, 90.471, 84.179, 83.391, 88.196, 81.427, 85.782, 85.737, 85.702]
2025-07-14 22:28:22,785 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53, 89.16, 88.73, 87.49, 87.17, 86.48, 86.13]
Average Accuracy (CNN): 88.85222222222222
2025-07-14 22:28:22,785 [trainer.py] => Average Accuracy (CNN): 88.85222222222222 

2025-07-14 22:28:22,786 [trainer.py] => All params: 88788289
2025-07-14 22:28:22,787 [trainer.py] => Trainable params: 2989633
2025-07-14 22:28:22,803 [ranpac.py] => Learning on 180-190
2025-07-14 22:28:25,484 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:28:25,486 [ranpac.py] => [Dynamic-K] Computed K values: [8, 13, 3, 10, 9, 8, 11, 8, 15, 9]
2025-07-14 22:28:25,486 [ranpac.py] => [KNN] task 9, dynamic K values: [8, 13, 3, 10, 9, 8, 11, 8, 15, 9]
2025-07-14 22:28:25,486 [ranpac.py] => [KNN] task 9, weight sparsity: 0.918, distance_metric: euclidean
2025-07-14 22:28:44,132 [trainer.py] => No NME accuracy.
2025-07-14 22:28:44,133 [trainer.py] => CNN: {'total': 85.45, '00-99': 89.08, '100-109': 90.54, '110-119': 77.11, '120-129': 75.17, '130-139': 86.21, '140-149': 70.86, '150-159': 84.59, '160-169': 83.89, '170-179': 84.56, '180-189': 79.09, 'old': 85.8, 'new': 79.09}
2025-07-14 22:28:44,133 [trainer.py] => CNN HM: [0.0, 90.471, 84.179, 83.391, 88.196, 81.427, 85.782, 85.737, 85.702, 82.308]
2025-07-14 22:28:44,133 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53, 89.16, 88.73, 87.49, 87.17, 86.48, 86.13, 85.45]
Average Accuracy (CNN): 88.512
2025-07-14 22:28:44,133 [trainer.py] => Average Accuracy (CNN): 88.512 

2025-07-14 22:28:44,134 [trainer.py] => All params: 88888289
2025-07-14 22:28:44,134 [trainer.py] => Trainable params: 3089633
2025-07-14 22:28:44,148 [ranpac.py] => Learning on 190-200
2025-07-14 22:28:47,062 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:28:47,063 [ranpac.py] => [Dynamic-K] Computed K values: [4, 12, 8, 8, 11, 6, 10, 3, 9, 8]
2025-07-14 22:28:47,063 [ranpac.py] => [KNN] task 10, dynamic K values: [4, 12, 8, 8, 11, 6, 10, 3, 9, 8]
2025-07-14 22:28:47,064 [ranpac.py] => [KNN] task 10, weight sparsity: 0.932, distance_metric: euclidean
2025-07-14 22:29:07,736 [trainer.py] => No NME accuracy.
2025-07-14 22:29:07,736 [trainer.py] => CNN: {'total': 84.67, '00-99': 88.53, '100-109': 90.54, '110-119': 77.82, '120-129': 75.17, '130-139': 86.55, '140-149': 71.58, '150-159': 84.59, '160-169': 82.55, '170-179': 84.9, '180-189': 78.4, '190-199': 75.68, 'old': 85.16, 'new': 75.68}
2025-07-14 22:29:07,736 [trainer.py] => CNN HM: [0.0, 90.471, 84.179, 83.391, 88.196, 81.427, 85.782, 85.737, 85.702, 82.308, 80.141]
2025-07-14 22:29:07,736 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53, 89.16, 88.73, 87.49, 87.17, 86.48, 86.13, 85.45, 84.67]
Average Accuracy (CNN): 88.16272727272727
2025-07-14 22:29:07,736 [trainer.py] => Average Accuracy (CNN): 88.16272727272727 

Accuracy Matrix (CNN):
[[92.44 91.79 91.85 91.09 90.64 90.19 90.16 89.81 89.39 89.08 88.53]
 [ 0.   89.19 89.86 91.22 91.55 92.23 90.54 90.88 90.54 90.54 90.54]
 [ 0.    0.   77.82 79.23 79.93 77.11 78.87 77.46 77.11 77.11 77.82]
 [ 0.    0.    0.   77.59 76.55 76.9  76.55 76.21 75.17 75.17 75.17]
 [ 0.    0.    0.    0.   87.59 87.93 87.59 87.24 86.9  86.21 86.55]
 [ 0.    0.    0.    0.    0.   75.54 74.82 69.42 71.22 70.86 71.58]
 [ 0.    0.    0.    0.    0.    0.   84.25 85.27 84.93 84.59 84.59]
 [ 0.    0.    0.    0.    0.    0.    0.   84.9  84.56 83.89 82.55]
 [ 0.    0.    0.    0.    0.    0.    0.    0.   85.23 84.56 84.9 ]
 [ 0.    0.    0.    0.    0.    0.    0.    0.    0.   79.09 78.4 ]
 [ 0.    0.    0.    0.    0.    0.    0.    0.    0.    0.   75.68]]
2025-07-14 22:29:07,737 [trainer.py] => Forgetting (CNN): 1.9520000000000024
