2025-07-14 18:43:39,429 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/ranpac.json
2025-07-14 18:43:39,430 [trainer.py] => prefix: reproduce
2025-07-14 18:43:39,430 [trainer.py] => dataset: cub
2025-07-14 18:43:39,430 [trainer.py] => memory_size: 0
2025-07-14 18:43:39,431 [trainer.py] => shuffle: True
2025-07-14 18:43:39,431 [trainer.py] => init_cls: 100
2025-07-14 18:43:39,431 [trainer.py] => increment: 10
2025-07-14 18:43:39,431 [trainer.py] => model_name: ranpac
2025-07-14 18:43:39,431 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 18:43:39,431 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 18:43:39,431 [trainer.py] => seed: 1993
2025-07-14 18:43:39,432 [trainer.py] => resume: False
2025-07-14 18:43:39,432 [trainer.py] => shot: 5
2025-07-14 18:43:39,432 [trainer.py] => use_simplecil: False
2025-07-14 18:43:39,432 [trainer.py] => tuned_epoch: 40
2025-07-14 18:43:39,432 [trainer.py] => init_lr: 0.01
2025-07-14 18:43:39,432 [trainer.py] => batch_size: 48
2025-07-14 18:43:39,432 [trainer.py] => weight_decay: 0.0005
2025-07-14 18:43:39,432 [trainer.py] => min_lr: 0
2025-07-14 18:43:39,433 [trainer.py] => ffn_num: 64
2025-07-14 18:43:39,433 [trainer.py] => optimizer: sgd
2025-07-14 18:43:39,433 [trainer.py] => use_RP: True
2025-07-14 18:43:39,433 [trainer.py] => M: 10000
2025-07-14 18:43:39,433 [trainer.py] => fecam: False
2025-07-14 18:43:39,433 [trainer.py] => calibration: True
2025-07-14 18:43:39,433 [trainer.py] => knn_k: 5
2025-07-14 18:43:39,433 [trainer.py] => knn_distance_metric: cosine
2025-07-14 18:43:39,433 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 18:43:39,434 [trainer.py] => knn_adaptive_k: False
2025-07-14 18:43:39,434 [trainer.py] => knn_temperature: 16.0
2025-07-14 18:43:39,582 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
This is for the BaseNet initialization.
I'm using ViT with adapters.
_IncompatibleKeys(missing_keys=['blocks.0.adaptmlp.down_proj.weight', 'blocks.0.adaptmlp.down_proj.bias', 'blocks.0.adaptmlp.up_proj.weight', 'blocks.0.adaptmlp.up_proj.bias', 'blocks.1.adaptmlp.down_proj.weight', 'blocks.1.adaptmlp.down_proj.bias', 'blocks.1.adaptmlp.up_proj.weight', 'blocks.1.adaptmlp.up_proj.bias', 'blocks.2.adaptmlp.down_proj.weight', 'blocks.2.adaptmlp.down_proj.bias', 'blocks.2.adaptmlp.up_proj.weight', 'blocks.2.adaptmlp.up_proj.bias', 'blocks.3.adaptmlp.down_proj.weight', 'blocks.3.adaptmlp.down_proj.bias', 'blocks.3.adaptmlp.up_proj.weight', 'blocks.3.adaptmlp.up_proj.bias', 'blocks.4.adaptmlp.down_proj.weight', 'blocks.4.adaptmlp.down_proj.bias', 'blocks.4.adaptmlp.up_proj.weight', 'blocks.4.adaptmlp.up_proj.bias', 'blocks.5.adaptmlp.down_proj.weight', 'blocks.5.adaptmlp.down_proj.bias', 'blocks.5.adaptmlp.up_proj.weight', 'blocks.5.adaptmlp.up_proj.bias', 'blocks.6.adaptmlp.down_proj.weight', 'blocks.6.adaptmlp.down_proj.bias', 'blocks.6.adaptmlp.up_proj.weight', 'blocks.6.adaptmlp.up_proj.bias', 'blocks.7.adaptmlp.down_proj.weight', 'blocks.7.adaptmlp.down_proj.bias', 'blocks.7.adaptmlp.up_proj.weight', 'blocks.7.adaptmlp.up_proj.bias', 'blocks.8.adaptmlp.down_proj.weight', 'blocks.8.adaptmlp.down_proj.bias', 'blocks.8.adaptmlp.up_proj.weight', 'blocks.8.adaptmlp.up_proj.bias', 'blocks.9.adaptmlp.down_proj.weight', 'blocks.9.adaptmlp.down_proj.bias', 'blocks.9.adaptmlp.up_proj.weight', 'blocks.9.adaptmlp.up_proj.bias', 'blocks.10.adaptmlp.down_proj.weight', 'blocks.10.adaptmlp.down_proj.bias', 'blocks.10.adaptmlp.up_proj.weight', 'blocks.10.adaptmlp.up_proj.bias', 'blocks.11.adaptmlp.down_proj.weight', 'blocks.11.adaptmlp.down_proj.bias', 'blocks.11.adaptmlp.up_proj.weight', 'blocks.11.adaptmlp.up_proj.bias'], unexpected_keys=[])
After BaseNet initialization.
2025-07-14 18:44:03,610 [trainer.py] => All params: 86988288
2025-07-14 18:44:03,610 [trainer.py] => Trainable params: 1189632
2025-07-14 18:44:04,847 [ranpac.py] => Learning on 0-100
87,065,089 total parameters.
1,266,433 training parameters.
backbone.blocks.0.adaptmlp.down_proj.weight 49152
backbone.blocks.0.adaptmlp.down_proj.bias 64
backbone.blocks.0.adaptmlp.up_proj.weight 49152
backbone.blocks.0.adaptmlp.up_proj.bias 768
backbone.blocks.1.adaptmlp.down_proj.weight 49152
backbone.blocks.1.adaptmlp.down_proj.bias 64
backbone.blocks.1.adaptmlp.up_proj.weight 49152
backbone.blocks.1.adaptmlp.up_proj.bias 768
backbone.blocks.2.adaptmlp.down_proj.weight 49152
backbone.blocks.2.adaptmlp.down_proj.bias 64
backbone.blocks.2.adaptmlp.up_proj.weight 49152
backbone.blocks.2.adaptmlp.up_proj.bias 768
backbone.blocks.3.adaptmlp.down_proj.weight 49152
backbone.blocks.3.adaptmlp.down_proj.bias 64
backbone.blocks.3.adaptmlp.up_proj.weight 49152
backbone.blocks.3.adaptmlp.up_proj.bias 768
backbone.blocks.4.adaptmlp.down_proj.weight 49152
backbone.blocks.4.adaptmlp.down_proj.bias 64
backbone.blocks.4.adaptmlp.up_proj.weight 49152
backbone.blocks.4.adaptmlp.up_proj.bias 768
backbone.blocks.5.adaptmlp.down_proj.weight 49152
backbone.blocks.5.adaptmlp.down_proj.bias 64
backbone.blocks.5.adaptmlp.up_proj.weight 49152
backbone.blocks.5.adaptmlp.up_proj.bias 768
backbone.blocks.6.adaptmlp.down_proj.weight 49152
backbone.blocks.6.adaptmlp.down_proj.bias 64
backbone.blocks.6.adaptmlp.up_proj.weight 49152
backbone.blocks.6.adaptmlp.up_proj.bias 768
backbone.blocks.7.adaptmlp.down_proj.weight 49152
backbone.blocks.7.adaptmlp.down_proj.bias 64
backbone.blocks.7.adaptmlp.up_proj.weight 49152
backbone.blocks.7.adaptmlp.up_proj.bias 768
backbone.blocks.8.adaptmlp.down_proj.weight 49152
backbone.blocks.8.adaptmlp.down_proj.bias 64
backbone.blocks.8.adaptmlp.up_proj.weight 49152
backbone.blocks.8.adaptmlp.up_proj.bias 768
backbone.blocks.9.adaptmlp.down_proj.weight 49152
backbone.blocks.9.adaptmlp.down_proj.bias 64
backbone.blocks.9.adaptmlp.up_proj.weight 49152
backbone.blocks.9.adaptmlp.up_proj.bias 768
backbone.blocks.10.adaptmlp.down_proj.weight 49152
backbone.blocks.10.adaptmlp.down_proj.bias 64
backbone.blocks.10.adaptmlp.up_proj.weight 49152
backbone.blocks.10.adaptmlp.up_proj.bias 768
backbone.blocks.11.adaptmlp.down_proj.weight 49152
backbone.blocks.11.adaptmlp.down_proj.bias 64
backbone.blocks.11.adaptmlp.up_proj.weight 49152
backbone.blocks.11.adaptmlp.up_proj.bias 768
fc.weight 76800
fc.sigma 1

  0%|          | 0/40 [00:00<?, ?it/s]
Task 0, Epoch 1/40 => Loss 4.586, Train_accy 4.84, Test_accy 14.84:   0%|          | 0/40 [00:38<?, ?it/s]
Task 0, Epoch 1/40 => Loss 4.586, Train_accy 4.84, Test_accy 14.84:   2%|▎         | 1/40 [00:38<25:13, 38.80s/it]
Task 0, Epoch 2/40 => Loss 4.519, Train_accy 23.96, Test_accy 43.64:   2%|▎         | 1/40 [01:13<25:13, 38.80s/it]
Task 0, Epoch 2/40 => Loss 4.519, Train_accy 23.96, Test_accy 43.64:   5%|▌         | 2/40 [01:13<23:07, 36.51s/it]
Task 0, Epoch 3/40 => Loss 4.366, Train_accy 45.91, Test_accy 63.67:   5%|▌         | 2/40 [01:50<23:07, 36.51s/it]
Task 0, Epoch 3/40 => Loss 4.366, Train_accy 45.91, Test_accy 63.67:   8%|▊         | 3/40 [01:50<22:30, 36.51s/it]
Task 0, Epoch 4/40 => Loss 3.998, Train_accy 61.86, Test_accy 74.80:   8%|▊         | 3/40 [02:28<22:30, 36.51s/it]
Task 0, Epoch 4/40 => Loss 3.998, Train_accy 61.86, Test_accy 74.80:  10%|█         | 4/40 [02:28<22:13, 37.03s/it]
Task 0, Epoch 5/40 => Loss 3.248, Train_accy 71.81, Test_accy 81.01:  10%|█         | 4/40 [03:03<22:13, 37.03s/it]
Task 0, Epoch 5/40 => Loss 3.248, Train_accy 71.81, Test_accy 81.01:  12%|█▎        | 5/40 [03:03<21:13, 36.39s/it]
Task 0, Epoch 6/40 => Loss 2.086, Train_accy 77.68, Test_accy 86.14:  12%|█▎        | 5/40 [03:42<21:13, 36.39s/it]
Task 0, Epoch 6/40 => Loss 2.086, Train_accy 77.68, Test_accy 86.14:  15%|█▌        | 6/40 [03:42<21:12, 37.42s/it]
Task 0, Epoch 7/40 => Loss 1.318, Train_accy 82.12, Test_accy 88.25:  15%|█▌        | 6/40 [04:20<21:12, 37.42s/it]
Task 0, Epoch 7/40 => Loss 1.318, Train_accy 82.12, Test_accy 88.25:  18%|█▊        | 7/40 [04:20<20:43, 37.68s/it]
Task 0, Epoch 8/40 => Loss 0.986, Train_accy 84.75, Test_accy 89.12:  18%|█▊        | 7/40 [04:58<20:43, 37.68s/it]
Task 0, Epoch 8/40 => Loss 0.986, Train_accy 84.75, Test_accy 89.12:  20%|██        | 8/40 [04:58<20:06, 37.70s/it]
Task 0, Epoch 9/40 => Loss 0.848, Train_accy 85.39, Test_accy 89.95:  20%|██        | 8/40 [05:40<20:06, 37.70s/it]
Task 0, Epoch 9/40 => Loss 0.848, Train_accy 85.39, Test_accy 89.95:  22%|██▎       | 9/40 [05:40<20:09, 39.02s/it]
Task 0, Epoch 10/40 => Loss 0.739, Train_accy 87.12, Test_accy 89.84:  22%|██▎       | 9/40 [06:21<20:09, 39.02s/it]
Task 0, Epoch 10/40 => Loss 0.739, Train_accy 87.12, Test_accy 89.84:  25%|██▌       | 10/40 [06:21<19:51, 39.73s/it]
Task 0, Epoch 11/40 => Loss 0.668, Train_accy 87.65, Test_accy 90.61:  25%|██▌       | 10/40 [07:11<19:51, 39.73s/it]
Task 0, Epoch 11/40 => Loss 0.668, Train_accy 87.65, Test_accy 90.61:  28%|██▊       | 11/40 [07:11<20:40, 42.77s/it]
Task 0, Epoch 12/40 => Loss 0.636, Train_accy 87.45, Test_accy 91.61:  28%|██▊       | 11/40 [07:52<20:40, 42.77s/it]
Task 0, Epoch 12/40 => Loss 0.636, Train_accy 87.45, Test_accy 91.61:  30%|███       | 12/40 [07:52<19:43, 42.26s/it]
Task 0, Epoch 13/40 => Loss 0.620, Train_accy 88.02, Test_accy 91.47:  30%|███       | 12/40 [08:31<19:43, 42.26s/it]
Task 0, Epoch 13/40 => Loss 0.620, Train_accy 88.02, Test_accy 91.47:  32%|███▎      | 13/40 [08:31<18:32, 41.21s/it]
Task 0, Epoch 14/40 => Loss 0.569, Train_accy 89.52, Test_accy 91.68:  32%|███▎      | 13/40 [09:09<18:32, 41.21s/it]
Task 0, Epoch 14/40 => Loss 0.569, Train_accy 89.52, Test_accy 91.68:  35%|███▌      | 14/40 [09:09<17:26, 40.26s/it]
Task 0, Epoch 15/40 => Loss 0.540, Train_accy 89.79, Test_accy 91.75:  35%|███▌      | 14/40 [09:53<17:26, 40.26s/it]
Task 0, Epoch 15/40 => Loss 0.540, Train_accy 89.79, Test_accy 91.75:  38%|███▊      | 15/40 [09:53<17:15, 41.40s/it]
Task 0, Epoch 16/40 => Loss 0.547, Train_accy 89.09, Test_accy 91.96:  38%|███▊      | 15/40 [10:32<17:15, 41.40s/it]
Task 0, Epoch 16/40 => Loss 0.547, Train_accy 89.09, Test_accy 91.96:  40%|████      | 16/40 [10:32<16:12, 40.53s/it]
Task 0, Epoch 17/40 => Loss 0.530, Train_accy 89.22, Test_accy 91.96:  40%|████      | 16/40 [11:18<16:12, 40.53s/it]
Task 0, Epoch 17/40 => Loss 0.530, Train_accy 89.22, Test_accy 91.96:  42%|████▎     | 17/40 [11:18<16:14, 42.35s/it]
Task 0, Epoch 18/40 => Loss 0.488, Train_accy 90.36, Test_accy 92.27:  42%|████▎     | 17/40 [12:01<16:14, 42.35s/it]
Task 0, Epoch 18/40 => Loss 0.488, Train_accy 90.36, Test_accy 92.27:  45%|████▌     | 18/40 [12:01<15:34, 42.48s/it]
Task 0, Epoch 19/40 => Loss 0.531, Train_accy 89.06, Test_accy 92.20:  45%|████▌     | 18/40 [12:46<15:34, 42.48s/it]
Task 0, Epoch 19/40 => Loss 0.531, Train_accy 89.06, Test_accy 92.20:  48%|████▊     | 19/40 [12:46<15:07, 43.21s/it]
Task 0, Epoch 20/40 => Loss 0.486, Train_accy 90.06, Test_accy 92.03:  48%|████▊     | 19/40 [13:23<15:07, 43.21s/it]
Task 0, Epoch 20/40 => Loss 0.486, Train_accy 90.06, Test_accy 92.03:  50%|█████     | 20/40 [13:23<13:47, 41.40s/it]
Task 0, Epoch 21/40 => Loss 0.443, Train_accy 91.79, Test_accy 92.10:  50%|█████     | 20/40 [14:07<13:47, 41.40s/it]
Task 0, Epoch 21/40 => Loss 0.443, Train_accy 91.79, Test_accy 92.10:  52%|█████▎    | 21/40 [14:07<13:20, 42.15s/it]
Task 0, Epoch 22/40 => Loss 0.431, Train_accy 91.62, Test_accy 92.44:  52%|█████▎    | 21/40 [14:44<13:20, 42.15s/it]
Task 0, Epoch 22/40 => Loss 0.431, Train_accy 91.62, Test_accy 92.44:  55%|█████▌    | 22/40 [14:44<12:10, 40.57s/it]
Task 0, Epoch 23/40 => Loss 0.443, Train_accy 91.26, Test_accy 92.37:  55%|█████▌    | 22/40 [15:24<12:10, 40.57s/it]
Task 0, Epoch 23/40 => Loss 0.443, Train_accy 91.26, Test_accy 92.37:  57%|█████▊    | 23/40 [15:24<11:28, 40.50s/it]
Task 0, Epoch 24/40 => Loss 0.442, Train_accy 91.59, Test_accy 92.37:  57%|█████▊    | 23/40 [16:02<11:28, 40.50s/it]
Task 0, Epoch 24/40 => Loss 0.442, Train_accy 91.59, Test_accy 92.37:  60%|██████    | 24/40 [16:02<10:34, 39.65s/it]
Task 0, Epoch 25/40 => Loss 0.433, Train_accy 91.12, Test_accy 92.44:  60%|██████    | 24/40 [16:46<10:34, 39.65s/it]
Task 0, Epoch 25/40 => Loss 0.433, Train_accy 91.12, Test_accy 92.44:  62%|██████▎   | 25/40 [16:46<10:13, 40.87s/it]
Task 0, Epoch 26/40 => Loss 0.421, Train_accy 91.83, Test_accy 92.37:  62%|██████▎   | 25/40 [17:20<10:13, 40.87s/it]
Task 0, Epoch 26/40 => Loss 0.421, Train_accy 91.83, Test_accy 92.37:  65%|██████▌   | 26/40 [17:20<09:03, 38.85s/it]
Task 0, Epoch 27/40 => Loss 0.417, Train_accy 92.46, Test_accy 92.17:  65%|██████▌   | 26/40 [17:55<09:03, 38.85s/it]
Task 0, Epoch 27/40 => Loss 0.417, Train_accy 92.46, Test_accy 92.17:  68%|██████▊   | 27/40 [17:55<08:12, 37.88s/it]
Task 0, Epoch 28/40 => Loss 0.409, Train_accy 91.52, Test_accy 92.34:  68%|██████▊   | 27/40 [18:34<08:12, 37.88s/it]
Task 0, Epoch 28/40 => Loss 0.409, Train_accy 91.52, Test_accy 92.34:  70%|███████   | 28/40 [18:34<07:38, 38.19s/it]
Task 0, Epoch 29/40 => Loss 0.397, Train_accy 91.89, Test_accy 92.31:  70%|███████   | 28/40 [19:07<07:38, 38.19s/it]
Task 0, Epoch 29/40 => Loss 0.397, Train_accy 91.89, Test_accy 92.31:  72%|███████▎  | 29/40 [19:07<06:43, 36.71s/it]
Task 0, Epoch 30/40 => Loss 0.417, Train_accy 91.79, Test_accy 92.34:  72%|███████▎  | 29/40 [19:42<06:43, 36.71s/it]
Task 0, Epoch 30/40 => Loss 0.417, Train_accy 91.79, Test_accy 92.34:  75%|███████▌  | 30/40 [19:42<05:59, 35.92s/it]
Task 0, Epoch 31/40 => Loss 0.400, Train_accy 92.43, Test_accy 92.34:  75%|███████▌  | 30/40 [20:21<05:59, 35.92s/it]
Task 0, Epoch 31/40 => Loss 0.400, Train_accy 92.43, Test_accy 92.34:  78%|███████▊  | 31/40 [20:21<05:33, 37.08s/it]
Task 0, Epoch 32/40 => Loss 0.408, Train_accy 92.26, Test_accy 92.41:  78%|███████▊  | 31/40 [21:05<05:33, 37.08s/it]
Task 0, Epoch 32/40 => Loss 0.408, Train_accy 92.26, Test_accy 92.41:  80%|████████  | 32/40 [21:05<05:12, 39.05s/it]
Task 0, Epoch 33/40 => Loss 0.405, Train_accy 92.43, Test_accy 92.44:  80%|████████  | 32/40 [21:44<05:12, 39.05s/it]
Task 0, Epoch 33/40 => Loss 0.405, Train_accy 92.43, Test_accy 92.44:  82%|████████▎ | 33/40 [21:44<04:32, 38.93s/it]
Task 0, Epoch 34/40 => Loss 0.408, Train_accy 92.09, Test_accy 92.48:  82%|████████▎ | 33/40 [22:21<04:32, 38.93s/it]
Task 0, Epoch 34/40 => Loss 0.408, Train_accy 92.09, Test_accy 92.48:  85%|████████▌ | 34/40 [22:21<03:49, 38.32s/it]
Task 0, Epoch 35/40 => Loss 0.389, Train_accy 92.46, Test_accy 92.55:  85%|████████▌ | 34/40 [23:01<03:49, 38.32s/it]
Task 0, Epoch 35/40 => Loss 0.389, Train_accy 92.46, Test_accy 92.55:  88%|████████▊ | 35/40 [23:01<03:14, 38.95s/it]
Task 0, Epoch 36/40 => Loss 0.406, Train_accy 91.89, Test_accy 92.48:  88%|████████▊ | 35/40 [23:39<03:14, 38.95s/it]
Task 0, Epoch 36/40 => Loss 0.406, Train_accy 91.89, Test_accy 92.48:  90%|█████████ | 36/40 [23:39<02:34, 38.74s/it]
Task 0, Epoch 37/40 => Loss 0.403, Train_accy 91.89, Test_accy 92.44:  90%|█████████ | 36/40 [24:18<02:34, 38.74s/it]
Task 0, Epoch 37/40 => Loss 0.403, Train_accy 91.89, Test_accy 92.44:  92%|█████████▎| 37/40 [24:18<01:55, 38.64s/it]
Task 0, Epoch 38/40 => Loss 0.411, Train_accy 92.09, Test_accy 92.48:  92%|█████████▎| 37/40 [25:02<01:55, 38.64s/it]
Task 0, Epoch 38/40 => Loss 0.411, Train_accy 92.09, Test_accy 92.48:  95%|█████████▌| 38/40 [25:02<01:20, 40.47s/it]
Task 0, Epoch 39/40 => Loss 0.394, Train_accy 92.09, Test_accy 92.48:  95%|█████████▌| 38/40 [25:37<01:20, 40.47s/it]
Task 0, Epoch 39/40 => Loss 0.394, Train_accy 92.09, Test_accy 92.48:  98%|█████████▊| 39/40 [25:37<00:38, 38.82s/it]
Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48:  98%|█████████▊| 39/40 [26:13<00:38, 38.82s/it]
Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48: 100%|██████████| 40/40 [26:13<00:00, 37.76s/it]
Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48: 100%|██████████| 40/40 [26:13<00:00, 39.33s/it]
2025-07-14 19:10:17,963 [ranpac.py] => Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48
selected lambda =  1000000.0
2025-07-14 19:11:04,872 [trainer.py] => No NME accuracy.
2025-07-14 19:11:04,874 [trainer.py] => CNN: {'total': 92.44, '00-99': 92.44, 'old': 0, 'new': 92.44}
2025-07-14 19:11:04,874 [trainer.py] => CNN HM: [0.0]
2025-07-14 19:11:04,875 [trainer.py] => CNN top1 curve: [92.44]
Average Accuracy (CNN): 92.44
2025-07-14 19:11:04,875 [trainer.py] => Average Accuracy (CNN): 92.44 

2025-07-14 19:11:04,877 [trainer.py] => All params: 87988289
2025-07-14 19:11:04,878 [trainer.py] => Trainable params: 1189633
2025-07-14 19:11:04,890 [ranpac.py] => Learning on 100-110
2025-07-14 19:11:09,029 [ranpac.py] => [KNN] task 1, fixed K=5
2025-07-14 19:11:09,033 [ranpac.py] => [KNN] task 1, weight sparsity: 0.950, distance_metric: cosine
/home/<USER>/workdir/FSCIL-Calibration-main/models/ranpac.py:113: RuntimeWarning: covariance is not positive-semidefinite.
  xx  = np.random.multivariate_normal(self.proto_list[class_index], self.cov_mats[class_index], 800)
2025-07-14 19:11:32,655 [trainer.py] => No NME accuracy.
2025-07-14 19:11:32,657 [trainer.py] => CNN: {'total': 91.48, '00-99': 91.72, '100-109': 89.19, 'old': 91.72, 'new': 89.19}
2025-07-14 19:11:32,657 [trainer.py] => CNN HM: [0.0, 90.437]
2025-07-14 19:11:32,658 [trainer.py] => CNN top1 curve: [92.44, 91.48]
Average Accuracy (CNN): 91.96000000000001
2025-07-14 19:11:32,658 [trainer.py] => Average Accuracy (CNN): 91.96000000000001 

2025-07-14 19:11:32,658 [trainer.py] => All params: 88088289
2025-07-14 19:11:32,659 [trainer.py] => Trainable params: 2289633
2025-07-14 19:11:32,669 [ranpac.py] => Learning on 110-120
2025-07-14 19:11:36,246 [ranpac.py] => [KNN] task 2, fixed K=5
2025-07-14 19:11:36,250 [ranpac.py] => [KNN] task 2, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:11:58,720 [trainer.py] => No NME accuracy.
2025-07-14 19:11:58,723 [trainer.py] => CNN: {'total': 90.3, '00-99': 91.61, '100-109': 89.86, '110-119': 77.46, 'old': 91.45, 'new': 77.46}
2025-07-14 19:11:58,723 [trainer.py] => CNN HM: [0.0, 90.437, 83.876]
2025-07-14 19:11:58,723 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3]
Average Accuracy (CNN): 91.40666666666668
2025-07-14 19:11:58,723 [trainer.py] => Average Accuracy (CNN): 91.40666666666668 

2025-07-14 19:11:58,725 [trainer.py] => All params: 88188289
2025-07-14 19:11:58,727 [trainer.py] => Trainable params: 2389633
2025-07-14 19:11:58,740 [ranpac.py] => Learning on 120-130
2025-07-14 19:12:02,265 [ranpac.py] => [KNN] task 3, fixed K=5
2025-07-14 19:12:02,266 [ranpac.py] => [KNN] task 3, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:12:27,210 [trainer.py] => No NME accuracy.
2025-07-14 19:12:27,212 [trainer.py] => CNN: {'total': 89.19, '00-99': 91.23, '100-109': 91.55, '110-119': 78.17, '120-129': 77.24, 'old': 90.19, 'new': 77.24}
2025-07-14 19:12:27,212 [trainer.py] => CNN HM: [0.0, 90.437, 83.876, 83.214]
2025-07-14 19:12:27,212 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3, 89.19]
Average Accuracy (CNN): 90.8525
2025-07-14 19:12:27,212 [trainer.py] => Average Accuracy (CNN): 90.8525 

2025-07-14 19:12:27,213 [trainer.py] => All params: 88288289
2025-07-14 19:12:27,213 [trainer.py] => Trainable params: 2489633
2025-07-14 19:12:27,230 [ranpac.py] => Learning on 130-140
2025-07-14 19:12:31,194 [ranpac.py] => [KNN] task 4, fixed K=5
2025-07-14 19:12:31,196 [ranpac.py] => [KNN] task 4, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:12:54,772 [trainer.py] => No NME accuracy.
2025-07-14 19:12:54,775 [trainer.py] => CNN: {'total': 88.48, '00-99': 90.68, '100-109': 92.57, '110-119': 78.52, '120-129': 74.14, '130-139': 86.55, 'old': 88.63, 'new': 86.55}
2025-07-14 19:12:54,775 [trainer.py] => CNN HM: [0.0, 90.437, 83.876, 83.214, 87.578]
2025-07-14 19:12:54,775 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3, 89.19, 88.48]
Average Accuracy (CNN): 90.37800000000001
2025-07-14 19:12:54,775 [trainer.py] => Average Accuracy (CNN): 90.37800000000001 

2025-07-14 19:12:54,777 [trainer.py] => All params: 88388289
2025-07-14 19:12:54,777 [trainer.py] => Trainable params: 2589633
2025-07-14 19:12:54,791 [ranpac.py] => Learning on 140-150
2025-07-14 19:12:58,072 [ranpac.py] => [KNN] task 5, fixed K=5
2025-07-14 19:12:58,075 [ranpac.py] => [KNN] task 5, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:13:28,332 [trainer.py] => No NME accuracy.
2025-07-14 19:13:28,334 [trainer.py] => CNN: {'total': 87.37, '00-99': 90.02, '100-109': 92.91, '110-119': 78.52, '120-129': 75.52, '130-139': 87.24, '140-149': 75.54, 'old': 88.18, 'new': 75.54}
2025-07-14 19:13:28,334 [trainer.py] => CNN HM: [0.0, 90.437, 83.876, 83.214, 87.578, 81.372]
2025-07-14 19:13:28,334 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3, 89.19, 88.48, 87.37]
Average Accuracy (CNN): 89.87666666666667
2025-07-14 19:13:28,334 [trainer.py] => Average Accuracy (CNN): 89.87666666666667 

2025-07-14 19:13:28,335 [trainer.py] => All params: 88488289
2025-07-14 19:13:28,336 [trainer.py] => Trainable params: 2689633
2025-07-14 19:13:28,353 [ranpac.py] => Learning on 150-160
2025-07-14 19:13:32,158 [ranpac.py] => [KNN] task 6, fixed K=5
2025-07-14 19:13:32,160 [ranpac.py] => [KNN] task 6, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:14:05,311 [trainer.py] => No NME accuracy.
2025-07-14 19:14:05,313 [trainer.py] => CNN: {'total': 87.09, '00-99': 89.98, '100-109': 90.54, '110-119': 79.23, '120-129': 76.55, '130-139': 87.24, '140-149': 75.18, '150-159': 84.25, 'old': 87.28, 'new': 84.25}
2025-07-14 19:14:05,314 [trainer.py] => CNN HM: [0.0, 90.437, 83.876, 83.214, 87.578, 81.372, 85.738]
2025-07-14 19:14:05,314 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3, 89.19, 88.48, 87.37, 87.09]
Average Accuracy (CNN): 89.47857142857143
2025-07-14 19:14:05,314 [trainer.py] => Average Accuracy (CNN): 89.47857142857143 

2025-07-14 19:14:05,315 [trainer.py] => All params: 88588289
2025-07-14 19:14:05,315 [trainer.py] => Trainable params: 2789633
2025-07-14 19:14:05,332 [ranpac.py] => Learning on 160-170
2025-07-14 19:14:09,414 [ranpac.py] => [KNN] task 7, fixed K=5
2025-07-14 19:14:09,417 [ranpac.py] => [KNN] task 7, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:14:37,014 [trainer.py] => No NME accuracy.
2025-07-14 19:14:37,016 [trainer.py] => CNN: {'total': 86.61, '00-99': 89.84, '100-109': 90.88, '110-119': 78.52, '120-129': 76.55, '130-139': 86.55, '140-149': 70.5, '150-159': 85.27, '160-169': 84.9, 'old': 86.72, 'new': 84.9}
2025-07-14 19:14:37,017 [trainer.py] => CNN HM: [0.0, 90.437, 83.876, 83.214, 87.578, 81.372, 85.738, 85.8]
2025-07-14 19:14:37,017 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3, 89.19, 88.48, 87.37, 87.09, 86.61]
Average Accuracy (CNN): 89.12
2025-07-14 19:14:37,017 [trainer.py] => Average Accuracy (CNN): 89.12 

2025-07-14 19:14:37,017 [trainer.py] => All params: 88688289
2025-07-14 19:14:37,018 [trainer.py] => Trainable params: 2889633
2025-07-14 19:14:37,033 [ranpac.py] => Learning on 170-180
2025-07-14 19:14:39,697 [ranpac.py] => [KNN] task 8, fixed K=5
2025-07-14 19:14:39,700 [ranpac.py] => [KNN] task 8, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:15:08,698 [trainer.py] => No NME accuracy.
2025-07-14 19:15:08,700 [trainer.py] => CNN: {'total': 86.34, '00-99': 89.5, '100-109': 90.88, '110-119': 78.17, '120-129': 75.52, '130-139': 86.9, '140-149': 70.86, '150-159': 84.59, '160-169': 85.57, '170-179': 85.91, 'old': 86.36, 'new': 85.91}
2025-07-14 19:15:08,700 [trainer.py] => CNN HM: [0.0, 90.437, 83.876, 83.214, 87.578, 81.372, 85.738, 85.8, 86.134]
2025-07-14 19:15:08,700 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3, 89.19, 88.48, 87.37, 87.09, 86.61, 86.34]
Average Accuracy (CNN): 88.81111111111112
2025-07-14 19:15:08,700 [trainer.py] => Average Accuracy (CNN): 88.81111111111112 

2025-07-14 19:15:08,701 [trainer.py] => All params: 88788289
2025-07-14 19:15:08,702 [trainer.py] => Trainable params: 2989633
2025-07-14 19:15:08,720 [ranpac.py] => Learning on 180-190
2025-07-14 19:15:13,601 [ranpac.py] => [KNN] task 9, fixed K=5
2025-07-14 19:15:13,604 [ranpac.py] => [KNN] task 9, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:15:44,036 [trainer.py] => No NME accuracy.
2025-07-14 19:15:44,038 [trainer.py] => CNN: {'total': 85.43, '00-99': 89.01, '100-109': 90.2, '110-119': 77.82, '120-129': 75.17, '130-139': 84.48, '140-149': 71.22, '150-159': 84.25, '160-169': 85.23, '170-179': 84.9, '180-189': 79.09, 'old': 85.78, 'new': 79.09}
2025-07-14 19:15:44,038 [trainer.py] => CNN HM: [0.0, 90.437, 83.876, 83.214, 87.578, 81.372, 85.738, 85.8, 86.134, 82.299]
2025-07-14 19:15:44,038 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3, 89.19, 88.48, 87.37, 87.09, 86.61, 86.34, 85.43]
Average Accuracy (CNN): 88.473
2025-07-14 19:15:44,039 [trainer.py] => Average Accuracy (CNN): 88.473 

2025-07-14 19:15:44,040 [trainer.py] => All params: 88888289
2025-07-14 19:15:44,041 [trainer.py] => Trainable params: 3089633
2025-07-14 19:15:44,057 [ranpac.py] => Learning on 190-200
2025-07-14 19:15:47,120 [ranpac.py] => [KNN] task 10, fixed K=5
2025-07-14 19:15:47,122 [ranpac.py] => [KNN] task 10, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:16:21,503 [trainer.py] => No NME accuracy.
2025-07-14 19:16:21,506 [trainer.py] => CNN: {'total': 84.76, '00-99': 88.18, '100-109': 90.54, '110-119': 78.52, '120-129': 75.17, '130-139': 85.17, '140-149': 71.94, '150-159': 85.62, '160-169': 83.89, '170-179': 84.56, '180-189': 79.79, '190-199': 77.7, 'old': 85.14, 'new': 77.7}
2025-07-14 19:16:21,506 [trainer.py] => CNN HM: [0.0, 90.437, 83.876, 83.214, 87.578, 81.372, 85.738, 85.8, 86.134, 82.299, 81.25]
2025-07-14 19:16:21,506 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3, 89.19, 88.48, 87.37, 87.09, 86.61, 86.34, 85.43, 84.76]
Average Accuracy (CNN): 88.13545454545455
2025-07-14 19:16:21,506 [trainer.py] => Average Accuracy (CNN): 88.13545454545455 

Accuracy Matrix (CNN):
[[92.44 91.72 91.61 91.23 90.68 90.02 89.98 89.84 89.5  89.01 88.18]
 [ 0.   89.19 89.86 91.55 92.57 92.91 90.54 90.88 90.88 90.2  90.54]
 [ 0.    0.   77.46 78.17 78.52 78.52 79.23 78.52 78.17 77.82 78.52]
 [ 0.    0.    0.   77.24 74.14 75.52 76.55 76.55 75.52 75.17 75.17]
 [ 0.    0.    0.    0.   86.55 87.24 87.24 86.55 86.9  84.48 85.17]
 [ 0.    0.    0.    0.    0.   75.54 75.18 70.5  70.86 71.22 71.94]
 [ 0.    0.    0.    0.    0.    0.   84.25 85.27 84.59 84.25 85.62]
 [ 0.    0.    0.    0.    0.    0.    0.   84.9  85.57 85.23 83.89]
 [ 0.    0.    0.    0.    0.    0.    0.    0.   85.91 84.9  84.56]
 [ 0.    0.    0.    0.    0.    0.    0.    0.    0.   79.09 79.79]
 [ 0.    0.    0.    0.    0.    0.    0.    0.    0.    0.   77.7 ]]
2025-07-14 19:16:21,508 [trainer.py] => Forgetting (CNN): 1.810999999999997
