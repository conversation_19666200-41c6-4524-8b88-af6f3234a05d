2025-07-14 22:06:21,785 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/ranpac_mahalanobis.json
2025-07-14 22:06:21,789 [trainer.py] => prefix: reproduce
2025-07-14 22:06:21,789 [trainer.py] => dataset: cub
2025-07-14 22:06:21,790 [trainer.py] => memory_size: 0
2025-07-14 22:06:21,790 [trainer.py] => shuffle: True
2025-07-14 22:06:21,790 [trainer.py] => init_cls: 100
2025-07-14 22:06:21,790 [trainer.py] => increment: 10
2025-07-14 22:06:21,790 [trainer.py] => model_name: ranpac
2025-07-14 22:06:21,790 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 22:06:21,790 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 22:06:21,791 [trainer.py] => seed: 1993
2025-07-14 22:06:21,791 [trainer.py] => resume: False
2025-07-14 22:06:21,791 [trainer.py] => shot: 5
2025-07-14 22:06:21,791 [trainer.py] => use_simplecil: False
2025-07-14 22:06:21,791 [trainer.py] => tuned_epoch: 40
2025-07-14 22:06:21,791 [trainer.py] => init_lr: 0.01
2025-07-14 22:06:21,791 [trainer.py] => batch_size: 48
2025-07-14 22:06:21,791 [trainer.py] => weight_decay: 0.0005
2025-07-14 22:06:21,792 [trainer.py] => min_lr: 0
2025-07-14 22:06:21,792 [trainer.py] => ffn_num: 64
2025-07-14 22:06:21,792 [trainer.py] => optimizer: sgd
2025-07-14 22:06:21,792 [trainer.py] => use_RP: True
2025-07-14 22:06:21,792 [trainer.py] => M: 10000
2025-07-14 22:06:21,792 [trainer.py] => fecam: False
2025-07-14 22:06:21,792 [trainer.py] => calibration: True
2025-07-14 22:06:21,793 [trainer.py] => knn_k: 5
2025-07-14 22:06:21,793 [trainer.py] => knn_distance_metric: mahalanobis
2025-07-14 22:06:21,793 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 22:06:21,793 [trainer.py] => knn_adaptive_k: True
2025-07-14 22:06:21,793 [trainer.py] => knn_temperature: 16.0
2025-07-14 22:06:21,793 [trainer.py] => k_min: 3
2025-07-14 22:06:21,793 [trainer.py] => k_max: 21
2025-07-14 22:06:21,793 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-14 22:06:21,794 [trainer.py] => cosine_temperature: 16.0
2025-07-14 22:06:21,910 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
This is for the BaseNet initialization.
I'm using ViT with adapters.
_IncompatibleKeys(missing_keys=['blocks.0.adaptmlp.down_proj.weight', 'blocks.0.adaptmlp.down_proj.bias', 'blocks.0.adaptmlp.up_proj.weight', 'blocks.0.adaptmlp.up_proj.bias', 'blocks.1.adaptmlp.down_proj.weight', 'blocks.1.adaptmlp.down_proj.bias', 'blocks.1.adaptmlp.up_proj.weight', 'blocks.1.adaptmlp.up_proj.bias', 'blocks.2.adaptmlp.down_proj.weight', 'blocks.2.adaptmlp.down_proj.bias', 'blocks.2.adaptmlp.up_proj.weight', 'blocks.2.adaptmlp.up_proj.bias', 'blocks.3.adaptmlp.down_proj.weight', 'blocks.3.adaptmlp.down_proj.bias', 'blocks.3.adaptmlp.up_proj.weight', 'blocks.3.adaptmlp.up_proj.bias', 'blocks.4.adaptmlp.down_proj.weight', 'blocks.4.adaptmlp.down_proj.bias', 'blocks.4.adaptmlp.up_proj.weight', 'blocks.4.adaptmlp.up_proj.bias', 'blocks.5.adaptmlp.down_proj.weight', 'blocks.5.adaptmlp.down_proj.bias', 'blocks.5.adaptmlp.up_proj.weight', 'blocks.5.adaptmlp.up_proj.bias', 'blocks.6.adaptmlp.down_proj.weight', 'blocks.6.adaptmlp.down_proj.bias', 'blocks.6.adaptmlp.up_proj.weight', 'blocks.6.adaptmlp.up_proj.bias', 'blocks.7.adaptmlp.down_proj.weight', 'blocks.7.adaptmlp.down_proj.bias', 'blocks.7.adaptmlp.up_proj.weight', 'blocks.7.adaptmlp.up_proj.bias', 'blocks.8.adaptmlp.down_proj.weight', 'blocks.8.adaptmlp.down_proj.bias', 'blocks.8.adaptmlp.up_proj.weight', 'blocks.8.adaptmlp.up_proj.bias', 'blocks.9.adaptmlp.down_proj.weight', 'blocks.9.adaptmlp.down_proj.bias', 'blocks.9.adaptmlp.up_proj.weight', 'blocks.9.adaptmlp.up_proj.bias', 'blocks.10.adaptmlp.down_proj.weight', 'blocks.10.adaptmlp.down_proj.bias', 'blocks.10.adaptmlp.up_proj.weight', 'blocks.10.adaptmlp.up_proj.bias', 'blocks.11.adaptmlp.down_proj.weight', 'blocks.11.adaptmlp.down_proj.bias', 'blocks.11.adaptmlp.up_proj.weight', 'blocks.11.adaptmlp.up_proj.bias'], unexpected_keys=[])
After BaseNet initialization.
2025-07-14 22:06:48,867 [trainer.py] => All params: 86988288
2025-07-14 22:06:48,870 [trainer.py] => Trainable params: 1189632
2025-07-14 22:07:04,414 [ranpac.py] => Learning on 0-100
87,065,089 total parameters.
1,266,433 training parameters.
backbone.blocks.0.adaptmlp.down_proj.weight 49152
backbone.blocks.0.adaptmlp.down_proj.bias 64
backbone.blocks.0.adaptmlp.up_proj.weight 49152
backbone.blocks.0.adaptmlp.up_proj.bias 768
backbone.blocks.1.adaptmlp.down_proj.weight 49152
backbone.blocks.1.adaptmlp.down_proj.bias 64
backbone.blocks.1.adaptmlp.up_proj.weight 49152
backbone.blocks.1.adaptmlp.up_proj.bias 768
backbone.blocks.2.adaptmlp.down_proj.weight 49152
backbone.blocks.2.adaptmlp.down_proj.bias 64
backbone.blocks.2.adaptmlp.up_proj.weight 49152
backbone.blocks.2.adaptmlp.up_proj.bias 768
backbone.blocks.3.adaptmlp.down_proj.weight 49152
backbone.blocks.3.adaptmlp.down_proj.bias 64
backbone.blocks.3.adaptmlp.up_proj.weight 49152
backbone.blocks.3.adaptmlp.up_proj.bias 768
backbone.blocks.4.adaptmlp.down_proj.weight 49152
backbone.blocks.4.adaptmlp.down_proj.bias 64
backbone.blocks.4.adaptmlp.up_proj.weight 49152
backbone.blocks.4.adaptmlp.up_proj.bias 768
backbone.blocks.5.adaptmlp.down_proj.weight 49152
backbone.blocks.5.adaptmlp.down_proj.bias 64
backbone.blocks.5.adaptmlp.up_proj.weight 49152
backbone.blocks.5.adaptmlp.up_proj.bias 768
backbone.blocks.6.adaptmlp.down_proj.weight 49152
backbone.blocks.6.adaptmlp.down_proj.bias 64
backbone.blocks.6.adaptmlp.up_proj.weight 49152
backbone.blocks.6.adaptmlp.up_proj.bias 768
backbone.blocks.7.adaptmlp.down_proj.weight 49152
backbone.blocks.7.adaptmlp.down_proj.bias 64
backbone.blocks.7.adaptmlp.up_proj.weight 49152
backbone.blocks.7.adaptmlp.up_proj.bias 768
backbone.blocks.8.adaptmlp.down_proj.weight 49152
backbone.blocks.8.adaptmlp.down_proj.bias 64
backbone.blocks.8.adaptmlp.up_proj.weight 49152
backbone.blocks.8.adaptmlp.up_proj.bias 768
backbone.blocks.9.adaptmlp.down_proj.weight 49152
backbone.blocks.9.adaptmlp.down_proj.bias 64
backbone.blocks.9.adaptmlp.up_proj.weight 49152
backbone.blocks.9.adaptmlp.up_proj.bias 768
backbone.blocks.10.adaptmlp.down_proj.weight 49152
backbone.blocks.10.adaptmlp.down_proj.bias 64
backbone.blocks.10.adaptmlp.up_proj.weight 49152
backbone.blocks.10.adaptmlp.up_proj.bias 768
backbone.blocks.11.adaptmlp.down_proj.weight 49152
backbone.blocks.11.adaptmlp.down_proj.bias 64
backbone.blocks.11.adaptmlp.up_proj.weight 49152
backbone.blocks.11.adaptmlp.up_proj.bias 768
fc.weight 76800
fc.sigma 1

  0%|          | 0/40 [00:00<?, ?it/s]
Task 0, Epoch 1/40 => Loss 4.586, Train_accy 4.84, Test_accy 14.84:   0%|          | 0/40 [00:59<?, ?it/s]
Task 0, Epoch 1/40 => Loss 4.586, Train_accy 4.84, Test_accy 14.84:   2%|▎         | 1/40 [00:59<38:46, 59.66s/it]
Task 0, Epoch 2/40 => Loss 4.519, Train_accy 23.96, Test_accy 43.64:   2%|▎         | 1/40 [01:27<38:46, 59.66s/it]
Task 0, Epoch 2/40 => Loss 4.519, Train_accy 23.96, Test_accy 43.64:   5%|▌         | 2/40 [01:27<26:06, 41.23s/it]
Task 0, Epoch 3/40 => Loss 4.366, Train_accy 45.91, Test_accy 63.67:   5%|▌         | 2/40 [02:01<26:06, 41.23s/it]
Task 0, Epoch 3/40 => Loss 4.366, Train_accy 45.91, Test_accy 63.67:   8%|▊         | 3/40 [02:01<23:23, 37.92s/it]
Task 0, Epoch 4/40 => Loss 3.998, Train_accy 61.86, Test_accy 74.80:   8%|▊         | 3/40 [02:21<23:23, 37.92s/it]
Task 0, Epoch 4/40 => Loss 3.998, Train_accy 61.86, Test_accy 74.80:  10%|█         | 4/40 [02:21<18:17, 30.49s/it]
Task 0, Epoch 5/40 => Loss 3.248, Train_accy 71.81, Test_accy 81.01:  10%|█         | 4/40 [02:40<18:17, 30.49s/it]
Task 0, Epoch 5/40 => Loss 3.248, Train_accy 71.81, Test_accy 81.01:  12%|█▎        | 5/40 [02:40<15:32, 26.66s/it]
Task 0, Epoch 6/40 => Loss 2.086, Train_accy 77.68, Test_accy 86.14:  12%|█▎        | 5/40 [03:10<15:32, 26.66s/it]
Task 0, Epoch 6/40 => Loss 2.086, Train_accy 77.68, Test_accy 86.14:  15%|█▌        | 6/40 [03:10<15:39, 27.64s/it]
Task 0, Epoch 7/40 => Loss 1.318, Train_accy 82.12, Test_accy 88.25:  15%|█▌        | 6/40 [03:41<15:39, 27.64s/it]
Task 0, Epoch 7/40 => Loss 1.318, Train_accy 82.12, Test_accy 88.25:  18%|█▊        | 7/40 [03:41<15:49, 28.79s/it]
Task 0, Epoch 8/40 => Loss 0.986, Train_accy 84.75, Test_accy 89.12:  18%|█▊        | 7/40 [04:01<15:49, 28.79s/it]
Task 0, Epoch 8/40 => Loss 0.986, Train_accy 84.75, Test_accy 89.12:  20%|██        | 8/40 [04:01<13:45, 25.81s/it]
Task 0, Epoch 9/40 => Loss 0.848, Train_accy 85.39, Test_accy 89.95:  20%|██        | 8/40 [04:19<13:45, 25.81s/it]
Task 0, Epoch 9/40 => Loss 0.848, Train_accy 85.39, Test_accy 89.95:  22%|██▎       | 9/40 [04:19<12:11, 23.58s/it]
Task 0, Epoch 10/40 => Loss 0.739, Train_accy 87.12, Test_accy 89.84:  22%|██▎       | 9/40 [04:38<12:11, 23.58s/it]
Task 0, Epoch 10/40 => Loss 0.739, Train_accy 87.12, Test_accy 89.84:  25%|██▌       | 10/40 [04:38<11:03, 22.10s/it]
Task 0, Epoch 11/40 => Loss 0.668, Train_accy 87.65, Test_accy 90.61:  25%|██▌       | 10/40 [05:00<11:03, 22.10s/it]
Task 0, Epoch 11/40 => Loss 0.668, Train_accy 87.65, Test_accy 90.61:  28%|██▊       | 11/40 [05:00<10:40, 22.10s/it]
Task 0, Epoch 12/40 => Loss 0.636, Train_accy 87.45, Test_accy 91.61:  28%|██▊       | 11/40 [05:35<10:40, 22.10s/it]
Task 0, Epoch 12/40 => Loss 0.636, Train_accy 87.45, Test_accy 91.61:  30%|███       | 12/40 [05:35<12:10, 26.08s/it]
Task 0, Epoch 13/40 => Loss 0.620, Train_accy 88.02, Test_accy 91.47:  30%|███       | 12/40 [05:59<12:10, 26.08s/it]
Task 0, Epoch 13/40 => Loss 0.620, Train_accy 88.02, Test_accy 91.47:  32%|███▎      | 13/40 [05:59<11:21, 25.23s/it]
Task 0, Epoch 14/40 => Loss 0.569, Train_accy 89.52, Test_accy 91.68:  32%|███▎      | 13/40 [06:22<11:21, 25.23s/it]
Task 0, Epoch 14/40 => Loss 0.569, Train_accy 89.52, Test_accy 91.68:  35%|███▌      | 14/40 [06:22<10:45, 24.82s/it]
Task 0, Epoch 15/40 => Loss 0.540, Train_accy 89.79, Test_accy 91.75:  35%|███▌      | 14/40 [06:46<10:45, 24.82s/it]
Task 0, Epoch 15/40 => Loss 0.540, Train_accy 89.79, Test_accy 91.75:  38%|███▊      | 15/40 [06:46<10:13, 24.55s/it]
Task 0, Epoch 16/40 => Loss 0.547, Train_accy 89.09, Test_accy 91.96:  38%|███▊      | 15/40 [07:05<10:13, 24.55s/it]
Task 0, Epoch 16/40 => Loss 0.547, Train_accy 89.09, Test_accy 91.96:  40%|████      | 16/40 [07:05<09:09, 22.91s/it]
Task 0, Epoch 17/40 => Loss 0.530, Train_accy 89.22, Test_accy 91.96:  40%|████      | 16/40 [07:24<09:09, 22.91s/it]
Task 0, Epoch 17/40 => Loss 0.530, Train_accy 89.22, Test_accy 91.96:  42%|████▎     | 17/40 [07:24<08:18, 21.69s/it]
Task 0, Epoch 18/40 => Loss 0.488, Train_accy 90.36, Test_accy 92.27:  42%|████▎     | 17/40 [07:45<08:18, 21.69s/it]
Task 0, Epoch 18/40 => Loss 0.488, Train_accy 90.36, Test_accy 92.27:  45%|████▌     | 18/40 [07:45<07:50, 21.41s/it]
Task 0, Epoch 19/40 => Loss 0.531, Train_accy 89.06, Test_accy 92.20:  45%|████▌     | 18/40 [08:09<07:50, 21.41s/it]
Task 0, Epoch 19/40 => Loss 0.531, Train_accy 89.06, Test_accy 92.20:  48%|████▊     | 19/40 [08:09<07:47, 22.26s/it]
Task 0, Epoch 20/40 => Loss 0.486, Train_accy 90.06, Test_accy 92.03:  48%|████▊     | 19/40 [08:39<07:47, 22.26s/it]
Task 0, Epoch 20/40 => Loss 0.486, Train_accy 90.06, Test_accy 92.03:  50%|█████     | 20/40 [08:39<08:12, 24.62s/it]
Task 0, Epoch 21/40 => Loss 0.443, Train_accy 91.79, Test_accy 92.10:  50%|█████     | 20/40 [09:06<08:12, 24.62s/it]
Task 0, Epoch 21/40 => Loss 0.443, Train_accy 91.79, Test_accy 92.10:  52%|█████▎    | 21/40 [09:06<07:57, 25.14s/it]
Task 0, Epoch 22/40 => Loss 0.431, Train_accy 91.62, Test_accy 92.44:  52%|█████▎    | 21/40 [09:42<07:57, 25.14s/it]
Task 0, Epoch 22/40 => Loss 0.431, Train_accy 91.62, Test_accy 92.44:  55%|█████▌    | 22/40 [09:42<08:29, 28.33s/it]
Task 0, Epoch 23/40 => Loss 0.443, Train_accy 91.26, Test_accy 92.37:  55%|█████▌    | 22/40 [10:06<08:29, 28.33s/it]
Task 0, Epoch 23/40 => Loss 0.443, Train_accy 91.26, Test_accy 92.37:  57%|█████▊    | 23/40 [10:06<07:39, 27.03s/it]
Task 0, Epoch 24/40 => Loss 0.442, Train_accy 91.59, Test_accy 92.37:  57%|█████▊    | 23/40 [10:24<07:39, 27.03s/it]
Task 0, Epoch 24/40 => Loss 0.442, Train_accy 91.59, Test_accy 92.37:  60%|██████    | 24/40 [10:24<06:33, 24.57s/it]
Task 0, Epoch 25/40 => Loss 0.433, Train_accy 91.12, Test_accy 92.44:  60%|██████    | 24/40 [10:46<06:33, 24.57s/it]
Task 0, Epoch 25/40 => Loss 0.433, Train_accy 91.12, Test_accy 92.44:  62%|██████▎   | 25/40 [10:46<05:53, 23.58s/it]
Task 0, Epoch 26/40 => Loss 0.421, Train_accy 91.83, Test_accy 92.37:  62%|██████▎   | 25/40 [11:11<05:53, 23.58s/it]
Task 0, Epoch 26/40 => Loss 0.421, Train_accy 91.83, Test_accy 92.37:  65%|██████▌   | 26/40 [11:11<05:36, 24.04s/it]
Task 0, Epoch 27/40 => Loss 0.417, Train_accy 92.46, Test_accy 92.17:  65%|██████▌   | 26/40 [11:43<05:36, 24.04s/it]
Task 0, Epoch 27/40 => Loss 0.417, Train_accy 92.46, Test_accy 92.17:  68%|██████▊   | 27/40 [11:43<05:43, 26.42s/it]
Task 0, Epoch 28/40 => Loss 0.409, Train_accy 91.52, Test_accy 92.34:  68%|██████▊   | 27/40 [12:05<05:43, 26.42s/it]
Task 0, Epoch 28/40 => Loss 0.409, Train_accy 91.52, Test_accy 92.34:  70%|███████   | 28/40 [12:05<05:01, 25.08s/it]
Task 0, Epoch 29/40 => Loss 0.397, Train_accy 91.89, Test_accy 92.31:  70%|███████   | 28/40 [12:40<05:01, 25.08s/it]
Task 0, Epoch 29/40 => Loss 0.397, Train_accy 91.89, Test_accy 92.31:  72%|███████▎  | 29/40 [12:40<05:10, 28.20s/it]
Task 0, Epoch 30/40 => Loss 0.417, Train_accy 91.79, Test_accy 92.34:  72%|███████▎  | 29/40 [13:14<05:10, 28.20s/it]
Task 0, Epoch 30/40 => Loss 0.417, Train_accy 91.79, Test_accy 92.34:  75%|███████▌  | 30/40 [13:14<04:57, 29.75s/it]
Task 0, Epoch 31/40 => Loss 0.400, Train_accy 92.43, Test_accy 92.34:  75%|███████▌  | 30/40 [13:37<04:57, 29.75s/it]
Task 0, Epoch 31/40 => Loss 0.400, Train_accy 92.43, Test_accy 92.34:  78%|███████▊  | 31/40 [13:37<04:10, 27.87s/it]
Task 0, Epoch 32/40 => Loss 0.408, Train_accy 92.26, Test_accy 92.41:  78%|███████▊  | 31/40 [13:56<04:10, 27.87s/it]
Task 0, Epoch 32/40 => Loss 0.408, Train_accy 92.26, Test_accy 92.41:  80%|████████  | 32/40 [13:56<03:22, 25.32s/it]
Task 0, Epoch 33/40 => Loss 0.405, Train_accy 92.43, Test_accy 92.44:  80%|████████  | 32/40 [14:24<03:22, 25.32s/it]
Task 0, Epoch 33/40 => Loss 0.405, Train_accy 92.43, Test_accy 92.44:  82%|████████▎ | 33/40 [14:24<03:02, 26.03s/it]
Task 0, Epoch 34/40 => Loss 0.408, Train_accy 92.09, Test_accy 92.48:  82%|████████▎ | 33/40 [14:58<03:02, 26.03s/it]
Task 0, Epoch 34/40 => Loss 0.408, Train_accy 92.09, Test_accy 92.48:  85%|████████▌ | 34/40 [14:58<02:49, 28.33s/it]
Task 0, Epoch 35/40 => Loss 0.389, Train_accy 92.46, Test_accy 92.55:  85%|████████▌ | 34/40 [15:18<02:49, 28.33s/it]
Task 0, Epoch 35/40 => Loss 0.389, Train_accy 92.46, Test_accy 92.55:  88%|████████▊ | 35/40 [15:18<02:09, 25.88s/it]
Task 0, Epoch 36/40 => Loss 0.406, Train_accy 91.89, Test_accy 92.48:  88%|████████▊ | 35/40 [15:43<02:09, 25.88s/it]
Task 0, Epoch 36/40 => Loss 0.406, Train_accy 91.89, Test_accy 92.48:  90%|█████████ | 36/40 [15:43<01:42, 25.74s/it]
Task 0, Epoch 37/40 => Loss 0.403, Train_accy 91.89, Test_accy 92.44:  90%|█████████ | 36/40 [16:12<01:42, 25.74s/it]
Task 0, Epoch 37/40 => Loss 0.403, Train_accy 91.89, Test_accy 92.44:  92%|█████████▎| 37/40 [16:12<01:19, 26.55s/it]
Task 0, Epoch 38/40 => Loss 0.411, Train_accy 92.09, Test_accy 92.48:  92%|█████████▎| 37/40 [16:44<01:19, 26.55s/it]
Task 0, Epoch 38/40 => Loss 0.411, Train_accy 92.09, Test_accy 92.48:  95%|█████████▌| 38/40 [16:44<00:56, 28.23s/it]
Task 0, Epoch 39/40 => Loss 0.394, Train_accy 92.09, Test_accy 92.48:  95%|█████████▌| 38/40 [17:20<00:56, 28.23s/it]
Task 0, Epoch 39/40 => Loss 0.394, Train_accy 92.09, Test_accy 92.48:  98%|█████████▊| 39/40 [17:20<00:30, 30.58s/it]
Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48:  98%|█████████▊| 39/40 [17:54<00:30, 30.58s/it]
Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48: 100%|██████████| 40/40 [17:54<00:00, 31.49s/it]
Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48: 100%|██████████| 40/40 [17:54<00:00, 26.85s/it]
2025-07-14 22:24:58,628 [ranpac.py] => Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48
selected lambda =  1000000.0
2025-07-14 22:25:28,450 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-14 22:25:28,905 [ranpac.py] => [Dynamic-K] Base similarity range: [-0.0264, 0.8900]
2025-07-14 22:25:28,905 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-14 22:25:28,905 [trainer.py] => No NME accuracy.
2025-07-14 22:25:28,905 [trainer.py] => CNN: {'total': 92.44, '00-99': 92.44, 'old': 0, 'new': 92.44}
2025-07-14 22:25:28,905 [trainer.py] => CNN HM: [0.0]
2025-07-14 22:25:28,906 [trainer.py] => CNN top1 curve: [92.44]
Average Accuracy (CNN): 92.44
2025-07-14 22:25:28,906 [trainer.py] => Average Accuracy (CNN): 92.44 

2025-07-14 22:25:28,906 [trainer.py] => All params: 87988289
2025-07-14 22:25:28,907 [trainer.py] => Trainable params: 1189633
2025-07-14 22:25:29,005 [ranpac.py] => Learning on 100-110
2025-07-14 22:26:09,298 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:26:09,302 [ranpac.py] => [Dynamic-K] Computed K values: [13, 14, 7, 14, 7, 7, 13, 10, 12, 8]
2025-07-14 22:26:09,302 [ranpac.py] => [KNN] task 1, dynamic K values: [13, 14, 7, 14, 7, 7, 13, 10, 12, 8]
2025-07-14 22:26:09,304 [ranpac.py] => [KNN] task 1, weight sparsity: 0.895, distance_metric: mahalanobis
/home/<USER>/workdir/FSCIL-Calibration-main/models/ranpac.py:137: RuntimeWarning: covariance is not positive-semidefinite.
  xx  = np.random.multivariate_normal(self.proto_list[class_index], self.cov_mats[class_index], 800)
2025-07-14 22:26:29,152 [trainer.py] => No NME accuracy.
2025-07-14 22:26:29,154 [trainer.py] => CNN: {'total': 91.45, '00-99': 91.65, '100-109': 89.53, 'old': 91.65, 'new': 89.53}
2025-07-14 22:26:29,155 [trainer.py] => CNN HM: [0.0, 90.578]
2025-07-14 22:26:29,155 [trainer.py] => CNN top1 curve: [92.44, 91.45]
Average Accuracy (CNN): 91.945
2025-07-14 22:26:29,156 [trainer.py] => Average Accuracy (CNN): 91.945 

2025-07-14 22:26:29,157 [trainer.py] => All params: 88088289
2025-07-14 22:26:29,159 [trainer.py] => Trainable params: 2289633
2025-07-14 22:26:29,175 [ranpac.py] => Learning on 110-120
2025-07-14 22:27:07,214 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:27:07,217 [ranpac.py] => [Dynamic-K] Computed K values: [7, 12, 9, 7, 13, 13, 13, 5, 13, 10]
2025-07-14 22:27:07,217 [ranpac.py] => [KNN] task 2, dynamic K values: [7, 12, 9, 7, 13, 13, 13, 5, 13, 10]
2025-07-14 22:27:07,218 [ranpac.py] => [KNN] task 2, weight sparsity: 0.898, distance_metric: mahalanobis
2025-07-14 22:27:22,343 [trainer.py] => No NME accuracy.
2025-07-14 22:27:22,346 [trainer.py] => CNN: {'total': 90.04, '00-99': 91.3, '100-109': 90.2, '110-119': 77.11, 'old': 91.2, 'new': 77.11}
2025-07-14 22:27:22,346 [trainer.py] => CNN HM: [0.0, 90.578, 83.565]
2025-07-14 22:27:22,346 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04]
Average Accuracy (CNN): 91.31
2025-07-14 22:27:22,346 [trainer.py] => Average Accuracy (CNN): 91.31 

2025-07-14 22:27:22,348 [trainer.py] => All params: 88188289
2025-07-14 22:27:22,350 [trainer.py] => Trainable params: 2389633
2025-07-14 22:27:22,364 [ranpac.py] => Learning on 120-130
2025-07-14 22:28:00,817 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:28:00,820 [ranpac.py] => [Dynamic-K] Computed K values: [8, 8, 10, 9, 8, 4, 5, 9, 4, 3]
2025-07-14 22:28:00,820 [ranpac.py] => [KNN] task 3, dynamic K values: [8, 8, 10, 9, 8, 4, 5, 9, 4, 3]
2025-07-14 22:28:00,821 [ranpac.py] => [KNN] task 3, weight sparsity: 0.932, distance_metric: mahalanobis
2025-07-14 22:28:13,145 [trainer.py] => No NME accuracy.
2025-07-14 22:28:13,148 [trainer.py] => CNN: {'total': 88.76, '00-99': 90.95, '100-109': 90.88, '110-119': 77.46, '120-129': 75.86, 'old': 89.84, 'new': 75.86}
2025-07-14 22:28:13,149 [trainer.py] => CNN HM: [0.0, 90.578, 83.565, 82.26]
2025-07-14 22:28:13,149 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04, 88.76]
Average Accuracy (CNN): 90.6725
2025-07-14 22:28:13,149 [trainer.py] => Average Accuracy (CNN): 90.6725 

2025-07-14 22:28:13,151 [trainer.py] => All params: 88288289
2025-07-14 22:28:13,153 [trainer.py] => Trainable params: 2489633
2025-07-14 22:28:13,169 [ranpac.py] => Learning on 130-140
2025-07-14 22:28:50,325 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:28:50,329 [ranpac.py] => [Dynamic-K] Computed K values: [13, 9, 15, 9, 12, 5, 7, 9, 10, 6]
2025-07-14 22:28:50,329 [ranpac.py] => [KNN] task 4, dynamic K values: [13, 9, 15, 9, 12, 5, 7, 9, 10, 6]
2025-07-14 22:28:50,330 [ranpac.py] => [KNN] task 4, weight sparsity: 0.905, distance_metric: mahalanobis
2025-07-14 22:29:04,953 [trainer.py] => No NME accuracy.
2025-07-14 22:29:04,955 [trainer.py] => CNN: {'total': 88.48, '00-99': 90.68, '100-109': 91.22, '110-119': 79.23, '120-129': 73.79, '130-139': 87.59, 'old': 88.55, 'new': 87.59}
2025-07-14 22:29:04,955 [trainer.py] => CNN HM: [0.0, 90.578, 83.565, 82.26, 88.067]
2025-07-14 22:29:04,955 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04, 88.76, 88.48]
Average Accuracy (CNN): 90.23400000000001
2025-07-14 22:29:04,955 [trainer.py] => Average Accuracy (CNN): 90.23400000000001 

2025-07-14 22:29:04,956 [trainer.py] => All params: 88388289
2025-07-14 22:29:04,957 [trainer.py] => Trainable params: 2589633
2025-07-14 22:29:04,971 [ranpac.py] => Learning on 140-150
2025-07-14 22:29:43,359 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:29:43,362 [ranpac.py] => [Dynamic-K] Computed K values: [9, 19, 10, 6, 7, 13, 15, 6, 3, 12]
2025-07-14 22:29:43,362 [ranpac.py] => [KNN] task 5, dynamic K values: [9, 19, 10, 6, 7, 13, 15, 6, 3, 12]
2025-07-14 22:29:43,363 [ranpac.py] => [KNN] task 5, weight sparsity: 0.900, distance_metric: mahalanobis
2025-07-14 22:30:10,696 [trainer.py] => No NME accuracy.
2025-07-14 22:30:10,698 [trainer.py] => CNN: {'total': 87.23, '00-99': 90.47, '100-109': 91.55, '110-119': 76.41, '120-129': 73.45, '130-139': 87.24, '140-149': 74.46, 'old': 88.11, 'new': 74.46}
2025-07-14 22:30:10,698 [trainer.py] => CNN HM: [0.0, 90.578, 83.565, 82.26, 88.067, 80.712]
2025-07-14 22:30:10,698 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04, 88.76, 88.48, 87.23]
Average Accuracy (CNN): 89.73333333333333
2025-07-14 22:30:10,699 [trainer.py] => Average Accuracy (CNN): 89.73333333333333 

2025-07-14 22:30:10,700 [trainer.py] => All params: 88488289
2025-07-14 22:30:10,701 [trainer.py] => Trainable params: 2689633
2025-07-14 22:30:10,718 [ranpac.py] => Learning on 150-160
2025-07-14 22:30:48,426 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:30:48,430 [ranpac.py] => [Dynamic-K] Computed K values: [11, 10, 8, 5, 8, 6, 8, 5, 17, 11]
2025-07-14 22:30:48,430 [ranpac.py] => [KNN] task 6, dynamic K values: [11, 10, 8, 5, 8, 6, 8, 5, 17, 11]
2025-07-14 22:30:48,430 [ranpac.py] => [KNN] task 6, weight sparsity: 0.911, distance_metric: mahalanobis
2025-07-14 22:31:17,741 [trainer.py] => No NME accuracy.
2025-07-14 22:31:17,743 [trainer.py] => CNN: {'total': 86.76, '00-99': 90.16, '100-109': 89.86, '110-119': 77.82, '120-129': 72.76, '130-139': 86.9, '140-149': 75.18, '150-159': 83.56, 'old': 86.98, 'new': 83.56}
2025-07-14 22:31:17,743 [trainer.py] => CNN HM: [0.0, 90.578, 83.565, 82.26, 88.067, 80.712, 85.236]
2025-07-14 22:31:17,744 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04, 88.76, 88.48, 87.23, 86.76]
Average Accuracy (CNN): 89.30857142857143
2025-07-14 22:31:17,745 [trainer.py] => Average Accuracy (CNN): 89.30857142857143 

2025-07-14 22:31:17,746 [trainer.py] => All params: 88588289
2025-07-14 22:31:17,748 [trainer.py] => Trainable params: 2789633
2025-07-14 22:31:17,767 [ranpac.py] => Learning on 160-170
2025-07-14 22:31:54,325 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:31:54,329 [ranpac.py] => [Dynamic-K] Computed K values: [18, 13, 10, 7, 12, 10, 6, 6, 9, 11]
2025-07-14 22:31:54,329 [ranpac.py] => [KNN] task 7, dynamic K values: [18, 13, 10, 7, 12, 10, 6, 6, 9, 11]
2025-07-14 22:31:54,330 [ranpac.py] => [KNN] task 7, weight sparsity: 0.898, distance_metric: mahalanobis
2025-07-14 22:32:10,383 [trainer.py] => No NME accuracy.
2025-07-14 22:32:10,385 [trainer.py] => CNN: {'total': 86.0, '00-99': 89.57, '100-109': 90.2, '110-119': 76.76, '120-129': 73.79, '130-139': 86.21, '140-149': 70.14, '150-159': 83.9, '160-169': 84.56, 'old': 86.09, 'new': 84.56}
2025-07-14 22:32:10,386 [trainer.py] => CNN HM: [0.0, 90.578, 83.565, 82.26, 88.067, 80.712, 85.236, 85.318]
2025-07-14 22:32:10,387 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04, 88.76, 88.48, 87.23, 86.76, 86.0]
Average Accuracy (CNN): 88.895
2025-07-14 22:32:10,387 [trainer.py] => Average Accuracy (CNN): 88.895 

2025-07-14 22:32:10,388 [trainer.py] => All params: 88688289
2025-07-14 22:32:10,389 [trainer.py] => Trainable params: 2889633
2025-07-14 22:32:10,408 [ranpac.py] => Learning on 170-180
2025-07-14 22:32:49,121 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:32:49,124 [ranpac.py] => [Dynamic-K] Computed K values: [14, 5, 11, 6, 10, 6, 7, 7, 13, 4]
2025-07-14 22:32:49,125 [ranpac.py] => [KNN] task 8, dynamic K values: [14, 5, 11, 6, 10, 6, 7, 7, 13, 4]
2025-07-14 22:32:49,126 [ranpac.py] => [KNN] task 8, weight sparsity: 0.917, distance_metric: mahalanobis
2025-07-14 22:33:21,655 [trainer.py] => No NME accuracy.
2025-07-14 22:33:21,657 [trainer.py] => CNN: {'total': 85.74, '00-99': 89.36, '100-109': 90.2, '110-119': 76.06, '120-129': 73.79, '130-139': 86.21, '140-149': 69.78, '150-159': 83.22, '160-169': 84.23, '170-179': 85.57, 'old': 85.75, 'new': 85.57}
2025-07-14 22:33:21,658 [trainer.py] => CNN HM: [0.0, 90.578, 83.565, 82.26, 88.067, 80.712, 85.236, 85.318, 85.66]
2025-07-14 22:33:21,658 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04, 88.76, 88.48, 87.23, 86.76, 86.0, 85.74]
Average Accuracy (CNN): 88.54444444444444
2025-07-14 22:33:21,659 [trainer.py] => Average Accuracy (CNN): 88.54444444444444 

2025-07-14 22:33:21,660 [trainer.py] => All params: 88788289
2025-07-14 22:33:21,661 [trainer.py] => Trainable params: 2989633
2025-07-14 22:33:21,679 [ranpac.py] => Learning on 180-190
2025-07-14 22:33:58,111 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:33:58,115 [ranpac.py] => [Dynamic-K] Computed K values: [8, 13, 3, 10, 9, 8, 11, 8, 15, 9]
2025-07-14 22:33:58,115 [ranpac.py] => [KNN] task 9, dynamic K values: [8, 13, 3, 10, 9, 8, 11, 8, 15, 9]
2025-07-14 22:33:58,116 [ranpac.py] => [KNN] task 9, weight sparsity: 0.906, distance_metric: mahalanobis
2025-07-14 22:34:32,536 [trainer.py] => No NME accuracy.
2025-07-14 22:34:32,538 [trainer.py] => CNN: {'total': 85.3, '00-99': 89.32, '100-109': 90.2, '110-119': 76.06, '120-129': 73.79, '130-139': 85.17, '140-149': 71.22, '150-159': 83.56, '160-169': 83.89, '170-179': 84.56, '180-189': 78.4, 'old': 85.68, 'new': 78.4}
2025-07-14 22:34:32,538 [trainer.py] => CNN HM: [0.0, 90.578, 83.565, 82.26, 88.067, 80.712, 85.236, 85.318, 85.66, 81.878]
2025-07-14 22:34:32,538 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04, 88.76, 88.48, 87.23, 86.76, 86.0, 85.74, 85.3]
Average Accuracy (CNN): 88.22
2025-07-14 22:34:32,538 [trainer.py] => Average Accuracy (CNN): 88.22 

2025-07-14 22:34:32,539 [trainer.py] => All params: 88888289
2025-07-14 22:34:32,540 [trainer.py] => Trainable params: 3089633
2025-07-14 22:34:32,556 [ranpac.py] => Learning on 190-200
2025-07-14 22:35:08,237 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:35:08,238 [ranpac.py] => [Dynamic-K] Computed K values: [4, 12, 8, 8, 11, 6, 10, 3, 9, 8]
2025-07-14 22:35:08,239 [ranpac.py] => [KNN] task 10, dynamic K values: [4, 12, 8, 8, 11, 6, 10, 3, 9, 8]
2025-07-14 22:35:08,239 [ranpac.py] => [KNN] task 10, weight sparsity: 0.921, distance_metric: mahalanobis
2025-07-14 22:35:27,523 [trainer.py] => No NME accuracy.
2025-07-14 22:35:27,524 [trainer.py] => CNN: {'total': 84.47, '00-99': 88.77, '100-109': 89.86, '110-119': 76.76, '120-129': 73.79, '130-139': 84.83, '140-149': 70.86, '150-159': 83.9, '160-169': 82.89, '170-179': 84.23, '180-189': 77.35, '190-199': 76.69, 'old': 84.89, 'new': 76.69}
2025-07-14 22:35:27,524 [trainer.py] => CNN HM: [0.0, 90.578, 83.565, 82.26, 88.067, 80.712, 85.236, 85.318, 85.66, 81.878, 80.582]
2025-07-14 22:35:27,524 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04, 88.76, 88.48, 87.23, 86.76, 86.0, 85.74, 85.3, 84.47]
Average Accuracy (CNN): 87.8790909090909
2025-07-14 22:35:27,524 [trainer.py] => Average Accuracy (CNN): 87.8790909090909 

Accuracy Matrix (CNN):
[[92.44 91.65 91.3  90.95 90.68 90.47 90.16 89.57 89.36 89.32 88.77]
 [ 0.   89.53 90.2  90.88 91.22 91.55 89.86 90.2  90.2  90.2  89.86]
 [ 0.    0.   77.11 77.46 79.23 76.41 77.82 76.76 76.06 76.06 76.76]
 [ 0.    0.    0.   75.86 73.79 73.45 72.76 73.79 73.79 73.79 73.79]
 [ 0.    0.    0.    0.   87.59 87.24 86.9  86.21 86.21 85.17 84.83]
 [ 0.    0.    0.    0.    0.   74.46 75.18 70.14 69.78 71.22 70.86]
 [ 0.    0.    0.    0.    0.    0.   83.56 83.9  83.22 83.56 83.9 ]
 [ 0.    0.    0.    0.    0.    0.    0.   84.56 84.23 83.89 82.89]
 [ 0.    0.    0.    0.    0.    0.    0.    0.   85.57 84.56 84.23]
 [ 0.    0.    0.    0.    0.    0.    0.    0.    0.   78.4  77.35]
 [ 0.    0.    0.    0.    0.    0.    0.    0.    0.    0.   76.69]]
2025-07-14 22:35:27,525 [trainer.py] => Forgetting (CNN): 2.1040000000000005
