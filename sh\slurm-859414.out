2025-07-14 21:17:03,231 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/ranpac.json
2025-07-14 21:17:03,234 [trainer.py] => prefix: reproduce
2025-07-14 21:17:03,234 [trainer.py] => dataset: cub
2025-07-14 21:17:03,235 [trainer.py] => memory_size: 0
2025-07-14 21:17:03,235 [trainer.py] => shuffle: True
2025-07-14 21:17:03,236 [trainer.py] => init_cls: 100
2025-07-14 21:17:03,236 [trainer.py] => increment: 10
2025-07-14 21:17:03,237 [trainer.py] => model_name: ranpac
2025-07-14 21:17:03,237 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 21:17:03,238 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 21:17:03,238 [trainer.py] => seed: 1993
2025-07-14 21:17:03,239 [trainer.py] => resume: False
2025-07-14 21:17:03,239 [trainer.py] => shot: 5
2025-07-14 21:17:03,239 [trainer.py] => use_simplecil: False
2025-07-14 21:17:03,240 [trainer.py] => tuned_epoch: 40
2025-07-14 21:17:03,240 [trainer.py] => init_lr: 0.01
2025-07-14 21:17:03,241 [trainer.py] => batch_size: 48
2025-07-14 21:17:03,242 [trainer.py] => weight_decay: 0.0005
2025-07-14 21:17:03,242 [trainer.py] => min_lr: 0
2025-07-14 21:17:03,242 [trainer.py] => ffn_num: 64
2025-07-14 21:17:03,242 [trainer.py] => optimizer: sgd
2025-07-14 21:17:03,242 [trainer.py] => use_RP: True
2025-07-14 21:17:03,242 [trainer.py] => M: 10000
2025-07-14 21:17:03,242 [trainer.py] => fecam: False
2025-07-14 21:17:03,242 [trainer.py] => calibration: True
2025-07-14 21:17:03,243 [trainer.py] => knn_k: 5
2025-07-14 21:17:03,243 [trainer.py] => knn_distance_metric: cosine
2025-07-14 21:17:03,243 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 21:17:03,243 [trainer.py] => knn_adaptive_k: True
2025-07-14 21:17:03,243 [trainer.py] => knn_temperature: 16.0
2025-07-14 21:17:03,243 [trainer.py] => k_min: 3
2025-07-14 21:17:03,243 [trainer.py] => k_max: 21
2025-07-14 21:17:03,243 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-14 21:17:03,244 [trainer.py] => cosine_temperature: 16.0
2025-07-14 21:17:03,360 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
This is for the BaseNet initialization.
I'm using ViT with adapters.
_IncompatibleKeys(missing_keys=['blocks.0.adaptmlp.down_proj.weight', 'blocks.0.adaptmlp.down_proj.bias', 'blocks.0.adaptmlp.up_proj.weight', 'blocks.0.adaptmlp.up_proj.bias', 'blocks.1.adaptmlp.down_proj.weight', 'blocks.1.adaptmlp.down_proj.bias', 'blocks.1.adaptmlp.up_proj.weight', 'blocks.1.adaptmlp.up_proj.bias', 'blocks.2.adaptmlp.down_proj.weight', 'blocks.2.adaptmlp.down_proj.bias', 'blocks.2.adaptmlp.up_proj.weight', 'blocks.2.adaptmlp.up_proj.bias', 'blocks.3.adaptmlp.down_proj.weight', 'blocks.3.adaptmlp.down_proj.bias', 'blocks.3.adaptmlp.up_proj.weight', 'blocks.3.adaptmlp.up_proj.bias', 'blocks.4.adaptmlp.down_proj.weight', 'blocks.4.adaptmlp.down_proj.bias', 'blocks.4.adaptmlp.up_proj.weight', 'blocks.4.adaptmlp.up_proj.bias', 'blocks.5.adaptmlp.down_proj.weight', 'blocks.5.adaptmlp.down_proj.bias', 'blocks.5.adaptmlp.up_proj.weight', 'blocks.5.adaptmlp.up_proj.bias', 'blocks.6.adaptmlp.down_proj.weight', 'blocks.6.adaptmlp.down_proj.bias', 'blocks.6.adaptmlp.up_proj.weight', 'blocks.6.adaptmlp.up_proj.bias', 'blocks.7.adaptmlp.down_proj.weight', 'blocks.7.adaptmlp.down_proj.bias', 'blocks.7.adaptmlp.up_proj.weight', 'blocks.7.adaptmlp.up_proj.bias', 'blocks.8.adaptmlp.down_proj.weight', 'blocks.8.adaptmlp.down_proj.bias', 'blocks.8.adaptmlp.up_proj.weight', 'blocks.8.adaptmlp.up_proj.bias', 'blocks.9.adaptmlp.down_proj.weight', 'blocks.9.adaptmlp.down_proj.bias', 'blocks.9.adaptmlp.up_proj.weight', 'blocks.9.adaptmlp.up_proj.bias', 'blocks.10.adaptmlp.down_proj.weight', 'blocks.10.adaptmlp.down_proj.bias', 'blocks.10.adaptmlp.up_proj.weight', 'blocks.10.adaptmlp.up_proj.bias', 'blocks.11.adaptmlp.down_proj.weight', 'blocks.11.adaptmlp.down_proj.bias', 'blocks.11.adaptmlp.up_proj.weight', 'blocks.11.adaptmlp.up_proj.bias'], unexpected_keys=[])
After BaseNet initialization.
2025-07-14 21:17:51,563 [trainer.py] => All params: 86988288
2025-07-14 21:17:51,566 [trainer.py] => Trainable params: 1189632
2025-07-14 21:18:12,816 [ranpac.py] => Learning on 0-100
87,065,089 total parameters.
1,266,433 training parameters.
backbone.blocks.0.adaptmlp.down_proj.weight 49152
backbone.blocks.0.adaptmlp.down_proj.bias 64
backbone.blocks.0.adaptmlp.up_proj.weight 49152
backbone.blocks.0.adaptmlp.up_proj.bias 768
backbone.blocks.1.adaptmlp.down_proj.weight 49152
backbone.blocks.1.adaptmlp.down_proj.bias 64
backbone.blocks.1.adaptmlp.up_proj.weight 49152
backbone.blocks.1.adaptmlp.up_proj.bias 768
backbone.blocks.2.adaptmlp.down_proj.weight 49152
backbone.blocks.2.adaptmlp.down_proj.bias 64
backbone.blocks.2.adaptmlp.up_proj.weight 49152
backbone.blocks.2.adaptmlp.up_proj.bias 768
backbone.blocks.3.adaptmlp.down_proj.weight 49152
backbone.blocks.3.adaptmlp.down_proj.bias 64
backbone.blocks.3.adaptmlp.up_proj.weight 49152
backbone.blocks.3.adaptmlp.up_proj.bias 768
backbone.blocks.4.adaptmlp.down_proj.weight 49152
backbone.blocks.4.adaptmlp.down_proj.bias 64
backbone.blocks.4.adaptmlp.up_proj.weight 49152
backbone.blocks.4.adaptmlp.up_proj.bias 768
backbone.blocks.5.adaptmlp.down_proj.weight 49152
backbone.blocks.5.adaptmlp.down_proj.bias 64
backbone.blocks.5.adaptmlp.up_proj.weight 49152
backbone.blocks.5.adaptmlp.up_proj.bias 768
backbone.blocks.6.adaptmlp.down_proj.weight 49152
backbone.blocks.6.adaptmlp.down_proj.bias 64
backbone.blocks.6.adaptmlp.up_proj.weight 49152
backbone.blocks.6.adaptmlp.up_proj.bias 768
backbone.blocks.7.adaptmlp.down_proj.weight 49152
backbone.blocks.7.adaptmlp.down_proj.bias 64
backbone.blocks.7.adaptmlp.up_proj.weight 49152
backbone.blocks.7.adaptmlp.up_proj.bias 768
backbone.blocks.8.adaptmlp.down_proj.weight 49152
backbone.blocks.8.adaptmlp.down_proj.bias 64
backbone.blocks.8.adaptmlp.up_proj.weight 49152
backbone.blocks.8.adaptmlp.up_proj.bias 768
backbone.blocks.9.adaptmlp.down_proj.weight 49152
backbone.blocks.9.adaptmlp.down_proj.bias 64
backbone.blocks.9.adaptmlp.up_proj.weight 49152
backbone.blocks.9.adaptmlp.up_proj.bias 768
backbone.blocks.10.adaptmlp.down_proj.weight 49152
backbone.blocks.10.adaptmlp.down_proj.bias 64
backbone.blocks.10.adaptmlp.up_proj.weight 49152
backbone.blocks.10.adaptmlp.up_proj.bias 768
backbone.blocks.11.adaptmlp.down_proj.weight 49152
backbone.blocks.11.adaptmlp.down_proj.bias 64
backbone.blocks.11.adaptmlp.up_proj.weight 49152
backbone.blocks.11.adaptmlp.up_proj.bias 768
fc.weight 76800
fc.sigma 1

  0%|          | 0/40 [00:00<?, ?it/s]
Task 0, Epoch 1/40 => Loss 4.586, Train_accy 4.84, Test_accy 14.84:   0%|          | 0/40 [01:16<?, ?it/s]
Task 0, Epoch 1/40 => Loss 4.586, Train_accy 4.84, Test_accy 14.84:   2%|▎         | 1/40 [01:16<49:33, 76.25s/it]
Task 0, Epoch 2/40 => Loss 4.519, Train_accy 23.96, Test_accy 43.64:   2%|▎         | 1/40 [01:56<49:33, 76.25s/it]
Task 0, Epoch 2/40 => Loss 4.519, Train_accy 23.96, Test_accy 43.64:   5%|▌         | 2/40 [01:56<34:43, 54.82s/it]
Task 0, Epoch 3/40 => Loss 4.366, Train_accy 45.91, Test_accy 63.67:   5%|▌         | 2/40 [02:37<34:43, 54.82s/it]
Task 0, Epoch 3/40 => Loss 4.366, Train_accy 45.91, Test_accy 63.67:   8%|▊         | 3/40 [02:37<29:56, 48.56s/it]
Task 0, Epoch 4/40 => Loss 3.998, Train_accy 61.86, Test_accy 74.80:   8%|▊         | 3/40 [03:18<29:56, 48.56s/it]
Task 0, Epoch 4/40 => Loss 3.998, Train_accy 61.86, Test_accy 74.80:  10%|█         | 4/40 [03:18<27:22, 45.64s/it]
Task 0, Epoch 5/40 => Loss 3.248, Train_accy 71.81, Test_accy 81.01:  10%|█         | 4/40 [03:56<27:22, 45.64s/it]
Task 0, Epoch 5/40 => Loss 3.248, Train_accy 71.81, Test_accy 81.01:  12%|█▎        | 5/40 [03:56<24:59, 42.84s/it]
Task 0, Epoch 6/40 => Loss 2.086, Train_accy 77.68, Test_accy 86.14:  12%|█▎        | 5/40 [04:31<24:59, 42.84s/it]
Task 0, Epoch 6/40 => Loss 2.086, Train_accy 77.68, Test_accy 86.14:  15%|█▌        | 6/40 [04:31<22:52, 40.37s/it]
Task 0, Epoch 7/40 => Loss 1.318, Train_accy 82.12, Test_accy 88.25:  15%|█▌        | 6/40 [05:08<22:52, 40.37s/it]
Task 0, Epoch 7/40 => Loss 1.318, Train_accy 82.12, Test_accy 88.25:  18%|█▊        | 7/40 [05:08<21:28, 39.04s/it]
Task 0, Epoch 8/40 => Loss 0.986, Train_accy 84.75, Test_accy 89.12:  18%|█▊        | 7/40 [05:50<21:28, 39.04s/it]
Task 0, Epoch 8/40 => Loss 0.986, Train_accy 84.75, Test_accy 89.12:  20%|██        | 8/40 [05:50<21:22, 40.08s/it]
Task 0, Epoch 9/40 => Loss 0.848, Train_accy 85.39, Test_accy 89.95:  20%|██        | 8/40 [06:29<21:22, 40.08s/it]
Task 0, Epoch 9/40 => Loss 0.848, Train_accy 85.39, Test_accy 89.95:  22%|██▎       | 9/40 [06:29<20:30, 39.70s/it]
Task 0, Epoch 10/40 => Loss 0.739, Train_accy 87.12, Test_accy 89.84:  22%|██▎       | 9/40 [07:06<20:30, 39.70s/it]
Task 0, Epoch 10/40 => Loss 0.739, Train_accy 87.12, Test_accy 89.84:  25%|██▌       | 10/40 [07:06<19:30, 39.01s/it]
Task 0, Epoch 11/40 => Loss 0.668, Train_accy 87.65, Test_accy 90.61:  25%|██▌       | 10/40 [07:42<19:30, 39.01s/it]
Task 0, Epoch 11/40 => Loss 0.668, Train_accy 87.65, Test_accy 90.61:  28%|██▊       | 11/40 [07:42<18:25, 38.12s/it]
Task 0, Epoch 12/40 => Loss 0.636, Train_accy 87.45, Test_accy 91.61:  28%|██▊       | 11/40 [08:18<18:25, 38.12s/it]
Task 0, Epoch 12/40 => Loss 0.636, Train_accy 87.45, Test_accy 91.61:  30%|███       | 12/40 [08:18<17:29, 37.48s/it]
Task 0, Epoch 13/40 => Loss 0.620, Train_accy 88.02, Test_accy 91.47:  30%|███       | 12/40 [08:56<17:29, 37.48s/it]
Task 0, Epoch 13/40 => Loss 0.620, Train_accy 88.02, Test_accy 91.47:  32%|███▎      | 13/40 [08:56<16:57, 37.68s/it]
Task 0, Epoch 14/40 => Loss 0.569, Train_accy 89.52, Test_accy 91.68:  32%|███▎      | 13/40 [09:35<16:57, 37.68s/it]
Task 0, Epoch 14/40 => Loss 0.569, Train_accy 89.52, Test_accy 91.68:  35%|███▌      | 14/40 [09:35<16:29, 38.06s/it]
Task 0, Epoch 15/40 => Loss 0.540, Train_accy 89.79, Test_accy 91.75:  35%|███▌      | 14/40 [10:11<16:29, 38.06s/it]
Task 0, Epoch 15/40 => Loss 0.540, Train_accy 89.79, Test_accy 91.75:  38%|███▊      | 15/40 [10:11<15:35, 37.44s/it]
Task 0, Epoch 16/40 => Loss 0.547, Train_accy 89.09, Test_accy 91.96:  38%|███▊      | 15/40 [10:44<15:35, 37.44s/it]
Task 0, Epoch 16/40 => Loss 0.547, Train_accy 89.09, Test_accy 91.96:  40%|████      | 16/40 [10:44<14:25, 36.07s/it]
Task 0, Epoch 17/40 => Loss 0.530, Train_accy 89.22, Test_accy 91.96:  40%|████      | 16/40 [11:16<14:25, 36.07s/it]
Task 0, Epoch 17/40 => Loss 0.530, Train_accy 89.22, Test_accy 91.96:  42%|████▎     | 17/40 [11:16<13:17, 34.65s/it]
Task 0, Epoch 18/40 => Loss 0.488, Train_accy 90.36, Test_accy 92.27:  42%|████▎     | 17/40 [11:43<13:17, 34.65s/it]
Task 0, Epoch 18/40 => Loss 0.488, Train_accy 90.36, Test_accy 92.27:  45%|████▌     | 18/40 [11:43<11:50, 32.30s/it]
Task 0, Epoch 19/40 => Loss 0.531, Train_accy 89.06, Test_accy 92.20:  45%|████▌     | 18/40 [12:17<11:50, 32.30s/it]
Task 0, Epoch 19/40 => Loss 0.531, Train_accy 89.06, Test_accy 92.20:  48%|████▊     | 19/40 [12:17<11:30, 32.87s/it]
Task 0, Epoch 20/40 => Loss 0.486, Train_accy 90.06, Test_accy 92.03:  48%|████▊     | 19/40 [12:53<11:30, 32.87s/it]
Task 0, Epoch 20/40 => Loss 0.486, Train_accy 90.06, Test_accy 92.03:  50%|█████     | 20/40 [12:53<11:18, 33.94s/it]
Task 0, Epoch 21/40 => Loss 0.443, Train_accy 91.79, Test_accy 92.10:  50%|█████     | 20/40 [13:23<11:18, 33.94s/it]
Task 0, Epoch 21/40 => Loss 0.443, Train_accy 91.79, Test_accy 92.10:  52%|█████▎    | 21/40 [13:23<10:22, 32.77s/it]
Task 0, Epoch 22/40 => Loss 0.431, Train_accy 91.62, Test_accy 92.44:  52%|█████▎    | 21/40 [13:58<10:22, 32.77s/it]
Task 0, Epoch 22/40 => Loss 0.431, Train_accy 91.62, Test_accy 92.44:  55%|█████▌    | 22/40 [13:58<09:58, 33.26s/it]
Task 0, Epoch 23/40 => Loss 0.443, Train_accy 91.26, Test_accy 92.37:  55%|█████▌    | 22/40 [14:29<09:58, 33.26s/it]
Task 0, Epoch 23/40 => Loss 0.443, Train_accy 91.26, Test_accy 92.37:  57%|█████▊    | 23/40 [14:29<09:16, 32.73s/it]
Task 0, Epoch 24/40 => Loss 0.442, Train_accy 91.59, Test_accy 92.37:  57%|█████▊    | 23/40 [15:05<09:16, 32.73s/it]
Task 0, Epoch 24/40 => Loss 0.442, Train_accy 91.59, Test_accy 92.37:  60%|██████    | 24/40 [15:05<08:58, 33.64s/it]
Task 0, Epoch 25/40 => Loss 0.433, Train_accy 91.12, Test_accy 92.44:  60%|██████    | 24/40 [15:42<08:58, 33.64s/it]
Task 0, Epoch 25/40 => Loss 0.433, Train_accy 91.12, Test_accy 92.44:  62%|██████▎   | 25/40 [15:42<08:41, 34.75s/it]
Task 0, Epoch 26/40 => Loss 0.421, Train_accy 91.83, Test_accy 92.37:  62%|██████▎   | 25/40 [16:07<08:41, 34.75s/it]
Task 0, Epoch 26/40 => Loss 0.421, Train_accy 91.83, Test_accy 92.37:  65%|██████▌   | 26/40 [16:07<07:25, 31.80s/it]
Task 0, Epoch 27/40 => Loss 0.417, Train_accy 92.46, Test_accy 92.17:  65%|██████▌   | 26/40 [16:35<07:25, 31.80s/it]
Task 0, Epoch 27/40 => Loss 0.417, Train_accy 92.46, Test_accy 92.17:  68%|██████▊   | 27/40 [16:35<06:40, 30.78s/it]
Task 0, Epoch 28/40 => Loss 0.409, Train_accy 91.52, Test_accy 92.34:  68%|██████▊   | 27/40 [17:03<06:40, 30.78s/it]
Task 0, Epoch 28/40 => Loss 0.409, Train_accy 91.52, Test_accy 92.34:  70%|███████   | 28/40 [17:03<05:57, 29.76s/it]
Task 0, Epoch 29/40 => Loss 0.397, Train_accy 91.89, Test_accy 92.31:  70%|███████   | 28/40 [17:33<05:57, 29.76s/it]
Task 0, Epoch 29/40 => Loss 0.397, Train_accy 91.89, Test_accy 92.31:  72%|███████▎  | 29/40 [17:33<05:29, 29.92s/it]
Task 0, Epoch 30/40 => Loss 0.417, Train_accy 91.79, Test_accy 92.34:  72%|███████▎  | 29/40 [18:01<05:29, 29.92s/it]
Task 0, Epoch 30/40 => Loss 0.417, Train_accy 91.79, Test_accy 92.34:  75%|███████▌  | 30/40 [18:01<04:53, 29.35s/it]
Task 0, Epoch 31/40 => Loss 0.400, Train_accy 92.43, Test_accy 92.34:  75%|███████▌  | 30/40 [18:34<04:53, 29.35s/it]
Task 0, Epoch 31/40 => Loss 0.400, Train_accy 92.43, Test_accy 92.34:  78%|███████▊  | 31/40 [18:34<04:33, 30.37s/it]
Task 0, Epoch 32/40 => Loss 0.408, Train_accy 92.26, Test_accy 92.41:  78%|███████▊  | 31/40 [19:02<04:33, 30.37s/it]
Task 0, Epoch 32/40 => Loss 0.408, Train_accy 92.26, Test_accy 92.41:  80%|████████  | 32/40 [19:02<03:57, 29.71s/it]
Task 0, Epoch 33/40 => Loss 0.405, Train_accy 92.43, Test_accy 92.44:  80%|████████  | 32/40 [19:32<03:57, 29.71s/it]
Task 0, Epoch 33/40 => Loss 0.405, Train_accy 92.43, Test_accy 92.44:  82%|████████▎ | 33/40 [19:32<03:28, 29.73s/it]
Task 0, Epoch 34/40 => Loss 0.408, Train_accy 92.09, Test_accy 92.48:  82%|████████▎ | 33/40 [20:07<03:28, 29.73s/it]
Task 0, Epoch 34/40 => Loss 0.408, Train_accy 92.09, Test_accy 92.48:  85%|████████▌ | 34/40 [20:07<03:07, 31.31s/it]
Task 0, Epoch 35/40 => Loss 0.389, Train_accy 92.46, Test_accy 92.55:  85%|████████▌ | 34/40 [20:40<03:07, 31.31s/it]
Task 0, Epoch 35/40 => Loss 0.389, Train_accy 92.46, Test_accy 92.55:  88%|████████▊ | 35/40 [20:40<02:39, 31.91s/it]
Task 0, Epoch 36/40 => Loss 0.406, Train_accy 91.89, Test_accy 92.48:  88%|████████▊ | 35/40 [21:17<02:39, 31.91s/it]
Task 0, Epoch 36/40 => Loss 0.406, Train_accy 91.89, Test_accy 92.48:  90%|█████████ | 36/40 [21:17<02:13, 33.39s/it]
Task 0, Epoch 37/40 => Loss 0.403, Train_accy 91.89, Test_accy 92.44:  90%|█████████ | 36/40 [21:49<02:13, 33.39s/it]
Task 0, Epoch 37/40 => Loss 0.403, Train_accy 91.89, Test_accy 92.44:  92%|█████████▎| 37/40 [21:49<01:39, 33.05s/it]
Task 0, Epoch 38/40 => Loss 0.411, Train_accy 92.09, Test_accy 92.48:  92%|█████████▎| 37/40 [22:21<01:39, 33.05s/it]
Task 0, Epoch 38/40 => Loss 0.411, Train_accy 92.09, Test_accy 92.48:  95%|█████████▌| 38/40 [22:21<01:05, 32.67s/it]
Task 0, Epoch 39/40 => Loss 0.394, Train_accy 92.09, Test_accy 92.48:  95%|█████████▌| 38/40 [22:51<01:05, 32.67s/it]
Task 0, Epoch 39/40 => Loss 0.394, Train_accy 92.09, Test_accy 92.48:  98%|█████████▊| 39/40 [22:51<00:31, 31.96s/it]
Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48:  98%|█████████▊| 39/40 [23:26<00:31, 31.96s/it]
Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48: 100%|██████████| 40/40 [23:26<00:00, 32.68s/it]
Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48: 100%|██████████| 40/40 [23:26<00:00, 35.16s/it]
2025-07-14 21:41:39,283 [ranpac.py] => Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48
selected lambda =  1000000.0
2025-07-14 21:42:20,756 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-14 21:42:21,203 [ranpac.py] => [Dynamic-K] Base similarity range: [-0.0264, 0.8900]
2025-07-14 21:42:21,204 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-14 21:42:21,204 [trainer.py] => No NME accuracy.
2025-07-14 21:42:21,204 [trainer.py] => CNN: {'total': 92.44, '00-99': 92.44, 'old': 0, 'new': 92.44}
2025-07-14 21:42:21,204 [trainer.py] => CNN HM: [0.0]
2025-07-14 21:42:21,204 [trainer.py] => CNN top1 curve: [92.44]
Average Accuracy (CNN): 92.44
2025-07-14 21:42:21,204 [trainer.py] => Average Accuracy (CNN): 92.44 

2025-07-14 21:42:21,204 [trainer.py] => All params: 87988289
2025-07-14 21:42:21,205 [trainer.py] => Trainable params: 1189633
2025-07-14 21:42:21,440 [ranpac.py] => Learning on 100-110
2025-07-14 21:42:24,718 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:42:24,741 [ranpac.py] => [Dynamic-K] Computed K values: [13, 14, 7, 14, 7, 7, 13, 10, 12, 8]
2025-07-14 21:42:24,742 [ranpac.py] => [KNN] task 1, dynamic K values: [13, 14, 7, 14, 7, 7, 13, 10, 12, 8]
2025-07-14 21:42:25,158 [ranpac.py] => [KNN] task 1, weight sparsity: 0.895, distance_metric: cosine
/home/<USER>/workdir/FSCIL-Calibration-main/models/ranpac.py:137: RuntimeWarning: covariance is not positive-semidefinite.
  xx  = np.random.multivariate_normal(self.proto_list[class_index], self.cov_mats[class_index], 800)
2025-07-14 21:42:45,922 [trainer.py] => No NME accuracy.
2025-07-14 21:42:45,924 [trainer.py] => CNN: {'total': 91.54, '00-99': 91.79, '100-109': 89.19, 'old': 91.79, 'new': 89.19}
2025-07-14 21:42:45,924 [trainer.py] => CNN HM: [0.0, 90.471]
2025-07-14 21:42:45,924 [trainer.py] => CNN top1 curve: [92.44, 91.54]
Average Accuracy (CNN): 91.99000000000001
2025-07-14 21:42:45,924 [trainer.py] => Average Accuracy (CNN): 91.99000000000001 

2025-07-14 21:42:45,925 [trainer.py] => All params: 88088289
2025-07-14 21:42:45,926 [trainer.py] => Trainable params: 2289633
2025-07-14 21:42:45,940 [ranpac.py] => Learning on 110-120
2025-07-14 21:42:48,695 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:42:48,697 [ranpac.py] => [Dynamic-K] Computed K values: [7, 12, 9, 7, 13, 13, 13, 5, 13, 10]
2025-07-14 21:42:48,697 [ranpac.py] => [KNN] task 2, dynamic K values: [7, 12, 9, 7, 13, 13, 13, 5, 13, 10]
2025-07-14 21:42:48,698 [ranpac.py] => [KNN] task 2, weight sparsity: 0.898, distance_metric: cosine
2025-07-14 21:43:11,070 [trainer.py] => No NME accuracy.
2025-07-14 21:43:11,072 [trainer.py] => CNN: {'total': 90.45, '00-99': 91.72, '100-109': 90.54, '110-119': 77.46, 'old': 91.61, 'new': 77.46}
2025-07-14 21:43:11,072 [trainer.py] => CNN HM: [0.0, 90.471, 83.943]
2025-07-14 21:43:11,072 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45]
Average Accuracy (CNN): 91.47666666666667
2025-07-14 21:43:11,072 [trainer.py] => Average Accuracy (CNN): 91.47666666666667 

2025-07-14 21:43:11,074 [trainer.py] => All params: 88188289
2025-07-14 21:43:11,076 [trainer.py] => Trainable params: 2389633
2025-07-14 21:43:11,089 [ranpac.py] => Learning on 120-130
2025-07-14 21:43:13,708 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:43:13,711 [ranpac.py] => [Dynamic-K] Computed K values: [8, 8, 10, 9, 8, 4, 5, 9, 4, 3]
2025-07-14 21:43:13,711 [ranpac.py] => [KNN] task 3, dynamic K values: [8, 8, 10, 9, 8, 4, 5, 9, 4, 3]
2025-07-14 21:43:13,712 [ranpac.py] => [KNN] task 3, weight sparsity: 0.932, distance_metric: cosine
2025-07-14 21:43:36,904 [trainer.py] => No NME accuracy.
2025-07-14 21:43:36,906 [trainer.py] => CNN: {'total': 89.19, '00-99': 91.27, '100-109': 91.55, '110-119': 78.52, '120-129': 76.55, 'old': 90.25, 'new': 76.55}
2025-07-14 21:43:36,906 [trainer.py] => CNN HM: [0.0, 90.471, 83.943, 82.837]
2025-07-14 21:43:36,906 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45, 89.19]
Average Accuracy (CNN): 90.905
2025-07-14 21:43:36,906 [trainer.py] => Average Accuracy (CNN): 90.905 

2025-07-14 21:43:36,908 [trainer.py] => All params: 88288289
2025-07-14 21:43:36,909 [trainer.py] => Trainable params: 2489633
2025-07-14 21:43:36,923 [ranpac.py] => Learning on 130-140
2025-07-14 21:43:39,661 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:43:39,665 [ranpac.py] => [Dynamic-K] Computed K values: [13, 9, 15, 9, 12, 5, 7, 9, 10, 6]
2025-07-14 21:43:39,666 [ranpac.py] => [KNN] task 4, dynamic K values: [13, 9, 15, 9, 12, 5, 7, 9, 10, 6]
2025-07-14 21:43:39,668 [ranpac.py] => [KNN] task 4, weight sparsity: 0.905, distance_metric: cosine
2025-07-14 21:44:08,956 [trainer.py] => No NME accuracy.
2025-07-14 21:44:08,958 [trainer.py] => CNN: {'total': 88.65, '00-99': 90.78, '100-109': 92.91, '110-119': 78.87, '120-129': 74.48, '130-139': 86.9, 'old': 88.79, 'new': 86.9}
2025-07-14 21:44:08,958 [trainer.py] => CNN HM: [0.0, 90.471, 83.943, 82.837, 87.835]
2025-07-14 21:44:08,959 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45, 89.19, 88.65]
Average Accuracy (CNN): 90.454
2025-07-14 21:44:08,959 [trainer.py] => Average Accuracy (CNN): 90.454 

2025-07-14 21:44:08,961 [trainer.py] => All params: 88388289
2025-07-14 21:44:08,962 [trainer.py] => Trainable params: 2589633
2025-07-14 21:44:08,980 [ranpac.py] => Learning on 140-150
2025-07-14 21:44:12,306 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:44:12,310 [ranpac.py] => [Dynamic-K] Computed K values: [9, 19, 10, 6, 7, 13, 15, 6, 3, 12]
2025-07-14 21:44:12,312 [ranpac.py] => [KNN] task 5, dynamic K values: [9, 19, 10, 6, 7, 13, 15, 6, 3, 12]
2025-07-14 21:44:12,313 [ranpac.py] => [KNN] task 5, weight sparsity: 0.900, distance_metric: cosine
2025-07-14 21:44:32,795 [trainer.py] => No NME accuracy.
2025-07-14 21:44:32,797 [trainer.py] => CNN: {'total': 87.49, '00-99': 90.12, '100-109': 93.24, '110-119': 78.52, '120-129': 75.52, '130-139': 87.24, '140-149': 75.9, 'old': 88.28, 'new': 75.9}
2025-07-14 21:44:32,798 [trainer.py] => CNN HM: [0.0, 90.471, 83.943, 82.837, 87.835, 81.623]
2025-07-14 21:44:32,798 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45, 89.19, 88.65, 87.49]
Average Accuracy (CNN): 89.96
2025-07-14 21:44:32,799 [trainer.py] => Average Accuracy (CNN): 89.96 

2025-07-14 21:44:32,800 [trainer.py] => All params: 88488289
2025-07-14 21:44:32,801 [trainer.py] => Trainable params: 2689633
2025-07-14 21:44:32,820 [ranpac.py] => Learning on 150-160
2025-07-14 21:44:37,125 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:44:37,128 [ranpac.py] => [Dynamic-K] Computed K values: [11, 10, 8, 5, 8, 6, 8, 5, 17, 11]
2025-07-14 21:44:37,129 [ranpac.py] => [KNN] task 6, dynamic K values: [11, 10, 8, 5, 8, 6, 8, 5, 17, 11]
2025-07-14 21:44:37,131 [ranpac.py] => [KNN] task 6, weight sparsity: 0.911, distance_metric: cosine
2025-07-14 21:44:58,753 [trainer.py] => No NME accuracy.
2025-07-14 21:44:58,756 [trainer.py] => CNN: {'total': 87.19, '00-99': 89.91, '100-109': 90.88, '110-119': 78.87, '120-129': 76.9, '130-139': 87.24, '140-149': 76.62, '150-159': 84.93, 'old': 87.35, 'new': 84.93}
2025-07-14 21:44:58,756 [trainer.py] => CNN HM: [0.0, 90.471, 83.943, 82.837, 87.835, 81.623, 86.123]
2025-07-14 21:44:58,756 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45, 89.19, 88.65, 87.49, 87.19]
Average Accuracy (CNN): 89.56428571428572
2025-07-14 21:44:58,756 [trainer.py] => Average Accuracy (CNN): 89.56428571428572 

2025-07-14 21:44:58,757 [trainer.py] => All params: 88588289
2025-07-14 21:44:58,758 [trainer.py] => Trainable params: 2789633
2025-07-14 21:44:58,775 [ranpac.py] => Learning on 160-170
2025-07-14 21:45:01,673 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:45:01,677 [ranpac.py] => [Dynamic-K] Computed K values: [18, 13, 10, 7, 12, 10, 6, 6, 9, 11]
2025-07-14 21:45:01,678 [ranpac.py] => [KNN] task 7, dynamic K values: [18, 13, 10, 7, 12, 10, 6, 6, 9, 11]
2025-07-14 21:45:01,680 [ranpac.py] => [KNN] task 7, weight sparsity: 0.898, distance_metric: cosine
2025-07-14 21:45:30,396 [trainer.py] => No NME accuracy.
2025-07-14 21:45:30,398 [trainer.py] => CNN: {'total': 86.75, '00-99': 89.84, '100-109': 90.88, '110-119': 78.87, '120-129': 76.9, '130-139': 86.55, '140-149': 71.58, '150-159': 85.62, '160-169': 85.23, 'old': 86.85, 'new': 85.23}
2025-07-14 21:45:30,398 [trainer.py] => CNN HM: [0.0, 90.471, 83.943, 82.837, 87.835, 81.623, 86.123, 86.032]
2025-07-14 21:45:30,398 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45, 89.19, 88.65, 87.49, 87.19, 86.75]
Average Accuracy (CNN): 89.2125
2025-07-14 21:45:30,399 [trainer.py] => Average Accuracy (CNN): 89.2125 

2025-07-14 21:45:30,400 [trainer.py] => All params: 88688289
2025-07-14 21:45:30,400 [trainer.py] => Trainable params: 2889633
2025-07-14 21:45:30,415 [ranpac.py] => Learning on 170-180
2025-07-14 21:45:33,338 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:45:33,340 [ranpac.py] => [Dynamic-K] Computed K values: [14, 5, 11, 6, 10, 6, 7, 7, 13, 4]
2025-07-14 21:45:33,340 [ranpac.py] => [KNN] task 8, dynamic K values: [14, 5, 11, 6, 10, 6, 7, 7, 13, 4]
2025-07-14 21:45:33,341 [ranpac.py] => [KNN] task 8, weight sparsity: 0.917, distance_metric: cosine
2025-07-14 21:45:59,847 [trainer.py] => No NME accuracy.
2025-07-14 21:45:59,849 [trainer.py] => CNN: {'total': 86.3, '00-99': 89.29, '100-109': 90.88, '110-119': 78.17, '120-129': 75.52, '130-139': 87.24, '140-149': 71.58, '150-159': 84.93, '160-169': 85.57, '170-179': 85.91, 'old': 86.32, 'new': 85.91}
2025-07-14 21:45:59,849 [trainer.py] => CNN HM: [0.0, 90.471, 83.943, 82.837, 87.835, 81.623, 86.123, 86.032, 86.115]
2025-07-14 21:45:59,849 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45, 89.19, 88.65, 87.49, 87.19, 86.75, 86.3]
Average Accuracy (CNN): 88.88888888888889
2025-07-14 21:45:59,849 [trainer.py] => Average Accuracy (CNN): 88.88888888888889 

2025-07-14 21:45:59,850 [trainer.py] => All params: 88788289
2025-07-14 21:45:59,851 [trainer.py] => Trainable params: 2989633
2025-07-14 21:45:59,866 [ranpac.py] => Learning on 180-190
2025-07-14 21:46:03,149 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:46:03,151 [ranpac.py] => [Dynamic-K] Computed K values: [8, 13, 3, 10, 9, 8, 11, 8, 15, 9]
2025-07-14 21:46:03,151 [ranpac.py] => [KNN] task 9, dynamic K values: [8, 13, 3, 10, 9, 8, 11, 8, 15, 9]
2025-07-14 21:46:03,151 [ranpac.py] => [KNN] task 9, weight sparsity: 0.906, distance_metric: cosine
2025-07-14 21:46:34,562 [trainer.py] => No NME accuracy.
2025-07-14 21:46:34,564 [trainer.py] => CNN: {'total': 85.5, '00-99': 88.8, '100-109': 90.2, '110-119': 78.17, '120-129': 75.52, '130-139': 85.17, '140-149': 72.66, '150-159': 84.93, '160-169': 85.23, '170-179': 84.56, '180-189': 79.44, 'old': 85.84, 'new': 79.44}
2025-07-14 21:46:34,564 [trainer.py] => CNN HM: [0.0, 90.471, 83.943, 82.837, 87.835, 81.623, 86.123, 86.032, 86.115, 82.516]
2025-07-14 21:46:34,564 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45, 89.19, 88.65, 87.49, 87.19, 86.75, 86.3, 85.5]
Average Accuracy (CNN): 88.55
2025-07-14 21:46:34,564 [trainer.py] => Average Accuracy (CNN): 88.55 

2025-07-14 21:46:34,566 [trainer.py] => All params: 88888289
2025-07-14 21:46:34,566 [trainer.py] => Trainable params: 3089633
2025-07-14 21:46:34,582 [ranpac.py] => Learning on 190-200
2025-07-14 21:46:37,948 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:46:37,949 [ranpac.py] => [Dynamic-K] Computed K values: [4, 12, 8, 8, 11, 6, 10, 3, 9, 8]
2025-07-14 21:46:37,949 [ranpac.py] => [KNN] task 10, dynamic K values: [4, 12, 8, 8, 11, 6, 10, 3, 9, 8]
2025-07-14 21:46:37,950 [ranpac.py] => [KNN] task 10, weight sparsity: 0.921, distance_metric: cosine
2025-07-14 21:47:04,916 [trainer.py] => No NME accuracy.
2025-07-14 21:47:04,919 [trainer.py] => CNN: {'total': 84.97, '00-99': 88.28, '100-109': 90.88, '110-119': 78.52, '120-129': 75.86, '130-139': 85.52, '140-149': 73.38, '150-159': 85.62, '160-169': 84.23, '170-179': 84.56, '180-189': 79.44, '190-199': 78.04, 'old': 85.34, 'new': 78.04}
2025-07-14 21:47:04,919 [trainer.py] => CNN HM: [0.0, 90.471, 83.943, 82.837, 87.835, 81.623, 86.123, 86.032, 86.115, 82.516, 81.527]
2025-07-14 21:47:04,920 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45, 89.19, 88.65, 87.49, 87.19, 86.75, 86.3, 85.5, 84.97]
Average Accuracy (CNN): 88.22454545454546
2025-07-14 21:47:04,920 [trainer.py] => Average Accuracy (CNN): 88.22454545454546 

Accuracy Matrix (CNN):
[[92.44 91.79 91.72 91.27 90.78 90.12 89.91 89.84 89.29 88.8  88.28]
 [ 0.   89.19 90.54 91.55 92.91 93.24 90.88 90.88 90.88 90.2  90.88]
 [ 0.    0.   77.46 78.52 78.87 78.52 78.87 78.87 78.17 78.17 78.52]
 [ 0.    0.    0.   76.55 74.48 75.52 76.9  76.9  75.52 75.52 75.86]
 [ 0.    0.    0.    0.   86.9  87.24 87.24 86.55 87.24 85.17 85.52]
 [ 0.    0.    0.    0.    0.   75.9  76.62 71.58 71.58 72.66 73.38]
 [ 0.    0.    0.    0.    0.    0.   84.93 85.62 84.93 84.93 85.62]
 [ 0.    0.    0.    0.    0.    0.    0.   85.23 85.57 85.23 84.23]
 [ 0.    0.    0.    0.    0.    0.    0.    0.   85.91 84.56 84.56]
 [ 0.    0.    0.    0.    0.    0.    0.    0.    0.   79.44 79.44]
 [ 0.    0.    0.    0.    0.    0.    0.    0.    0.    0.   78.04]]
2025-07-14 21:47:04,922 [trainer.py] => Forgetting (CNN): 1.5560000000000003
