2025-07-14 18:35:29,873 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/ranpac.json
2025-07-14 18:35:29,878 [trainer.py] => prefix: reproduce
2025-07-14 18:35:29,878 [trainer.py] => dataset: cub
2025-07-14 18:35:29,878 [trainer.py] => memory_size: 0
2025-07-14 18:35:29,878 [trainer.py] => shuffle: True
2025-07-14 18:35:29,878 [trainer.py] => init_cls: 100
2025-07-14 18:35:29,878 [trainer.py] => increment: 10
2025-07-14 18:35:29,879 [trainer.py] => model_name: ranpac
2025-07-14 18:35:29,879 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 18:35:29,879 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 18:35:29,879 [trainer.py] => seed: 1993
2025-07-14 18:35:29,879 [trainer.py] => resume: False
2025-07-14 18:35:29,879 [trainer.py] => shot: 5
2025-07-14 18:35:29,879 [trainer.py] => use_simplecil: False
2025-07-14 18:35:29,879 [trainer.py] => tuned_epoch: 1
2025-07-14 18:35:29,880 [trainer.py] => init_lr: 0.01
2025-07-14 18:35:29,880 [trainer.py] => batch_size: 48
2025-07-14 18:35:29,880 [trainer.py] => weight_decay: 0.0005
2025-07-14 18:35:29,880 [trainer.py] => min_lr: 0
2025-07-14 18:35:29,880 [trainer.py] => ffn_num: 64
2025-07-14 18:35:29,880 [trainer.py] => optimizer: sgd
2025-07-14 18:35:29,880 [trainer.py] => use_RP: True
2025-07-14 18:35:29,881 [trainer.py] => M: 10000
2025-07-14 18:35:29,881 [trainer.py] => fecam: False
2025-07-14 18:35:29,881 [trainer.py] => calibration: True
2025-07-14 18:35:29,881 [trainer.py] => knn_k: 5
2025-07-14 18:35:29,881 [trainer.py] => knn_distance_metric: cosine
2025-07-14 18:35:29,881 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 18:35:29,881 [trainer.py] => knn_adaptive_k: False
2025-07-14 18:35:29,881 [trainer.py] => knn_temperature: 16.0
2025-07-14 18:35:30,048 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
This is for the BaseNet initialization.
I'm using ViT with adapters.
_IncompatibleKeys(missing_keys=['blocks.0.adaptmlp.down_proj.weight', 'blocks.0.adaptmlp.down_proj.bias', 'blocks.0.adaptmlp.up_proj.weight', 'blocks.0.adaptmlp.up_proj.bias', 'blocks.1.adaptmlp.down_proj.weight', 'blocks.1.adaptmlp.down_proj.bias', 'blocks.1.adaptmlp.up_proj.weight', 'blocks.1.adaptmlp.up_proj.bias', 'blocks.2.adaptmlp.down_proj.weight', 'blocks.2.adaptmlp.down_proj.bias', 'blocks.2.adaptmlp.up_proj.weight', 'blocks.2.adaptmlp.up_proj.bias', 'blocks.3.adaptmlp.down_proj.weight', 'blocks.3.adaptmlp.down_proj.bias', 'blocks.3.adaptmlp.up_proj.weight', 'blocks.3.adaptmlp.up_proj.bias', 'blocks.4.adaptmlp.down_proj.weight', 'blocks.4.adaptmlp.down_proj.bias', 'blocks.4.adaptmlp.up_proj.weight', 'blocks.4.adaptmlp.up_proj.bias', 'blocks.5.adaptmlp.down_proj.weight', 'blocks.5.adaptmlp.down_proj.bias', 'blocks.5.adaptmlp.up_proj.weight', 'blocks.5.adaptmlp.up_proj.bias', 'blocks.6.adaptmlp.down_proj.weight', 'blocks.6.adaptmlp.down_proj.bias', 'blocks.6.adaptmlp.up_proj.weight', 'blocks.6.adaptmlp.up_proj.bias', 'blocks.7.adaptmlp.down_proj.weight', 'blocks.7.adaptmlp.down_proj.bias', 'blocks.7.adaptmlp.up_proj.weight', 'blocks.7.adaptmlp.up_proj.bias', 'blocks.8.adaptmlp.down_proj.weight', 'blocks.8.adaptmlp.down_proj.bias', 'blocks.8.adaptmlp.up_proj.weight', 'blocks.8.adaptmlp.up_proj.bias', 'blocks.9.adaptmlp.down_proj.weight', 'blocks.9.adaptmlp.down_proj.bias', 'blocks.9.adaptmlp.up_proj.weight', 'blocks.9.adaptmlp.up_proj.bias', 'blocks.10.adaptmlp.down_proj.weight', 'blocks.10.adaptmlp.down_proj.bias', 'blocks.10.adaptmlp.up_proj.weight', 'blocks.10.adaptmlp.up_proj.bias', 'blocks.11.adaptmlp.down_proj.weight', 'blocks.11.adaptmlp.down_proj.bias', 'blocks.11.adaptmlp.up_proj.weight', 'blocks.11.adaptmlp.up_proj.bias'], unexpected_keys=[])
After BaseNet initialization.
2025-07-14 18:36:00,142 [trainer.py] => All params: 86988288
2025-07-14 18:36:00,143 [trainer.py] => Trainable params: 1189632
2025-07-14 18:36:01,384 [ranpac.py] => Learning on 0-100
87,065,089 total parameters.
1,266,433 training parameters.
backbone.blocks.0.adaptmlp.down_proj.weight 49152
backbone.blocks.0.adaptmlp.down_proj.bias 64
backbone.blocks.0.adaptmlp.up_proj.weight 49152
backbone.blocks.0.adaptmlp.up_proj.bias 768
backbone.blocks.1.adaptmlp.down_proj.weight 49152
backbone.blocks.1.adaptmlp.down_proj.bias 64
backbone.blocks.1.adaptmlp.up_proj.weight 49152
backbone.blocks.1.adaptmlp.up_proj.bias 768
backbone.blocks.2.adaptmlp.down_proj.weight 49152
backbone.blocks.2.adaptmlp.down_proj.bias 64
backbone.blocks.2.adaptmlp.up_proj.weight 49152
backbone.blocks.2.adaptmlp.up_proj.bias 768
backbone.blocks.3.adaptmlp.down_proj.weight 49152
backbone.blocks.3.adaptmlp.down_proj.bias 64
backbone.blocks.3.adaptmlp.up_proj.weight 49152
backbone.blocks.3.adaptmlp.up_proj.bias 768
backbone.blocks.4.adaptmlp.down_proj.weight 49152
backbone.blocks.4.adaptmlp.down_proj.bias 64
backbone.blocks.4.adaptmlp.up_proj.weight 49152
backbone.blocks.4.adaptmlp.up_proj.bias 768
backbone.blocks.5.adaptmlp.down_proj.weight 49152
backbone.blocks.5.adaptmlp.down_proj.bias 64
backbone.blocks.5.adaptmlp.up_proj.weight 49152
backbone.blocks.5.adaptmlp.up_proj.bias 768
backbone.blocks.6.adaptmlp.down_proj.weight 49152
backbone.blocks.6.adaptmlp.down_proj.bias 64
backbone.blocks.6.adaptmlp.up_proj.weight 49152
backbone.blocks.6.adaptmlp.up_proj.bias 768
backbone.blocks.7.adaptmlp.down_proj.weight 49152
backbone.blocks.7.adaptmlp.down_proj.bias 64
backbone.blocks.7.adaptmlp.up_proj.weight 49152
backbone.blocks.7.adaptmlp.up_proj.bias 768
backbone.blocks.8.adaptmlp.down_proj.weight 49152
backbone.blocks.8.adaptmlp.down_proj.bias 64
backbone.blocks.8.adaptmlp.up_proj.weight 49152
backbone.blocks.8.adaptmlp.up_proj.bias 768
backbone.blocks.9.adaptmlp.down_proj.weight 49152
backbone.blocks.9.adaptmlp.down_proj.bias 64
backbone.blocks.9.adaptmlp.up_proj.weight 49152
backbone.blocks.9.adaptmlp.up_proj.bias 768
backbone.blocks.10.adaptmlp.down_proj.weight 49152
backbone.blocks.10.adaptmlp.down_proj.bias 64
backbone.blocks.10.adaptmlp.up_proj.weight 49152
backbone.blocks.10.adaptmlp.up_proj.bias 768
backbone.blocks.11.adaptmlp.down_proj.weight 49152
backbone.blocks.11.adaptmlp.down_proj.bias 64
backbone.blocks.11.adaptmlp.up_proj.weight 49152
backbone.blocks.11.adaptmlp.up_proj.bias 768
fc.weight 76800
fc.sigma 1

  0%|          | 0/1 [00:00<?, ?it/s]
Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84:   0%|          | 0/1 [00:38<?, ?it/s]
Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84: 100%|██████████| 1/1 [00:38<00:00, 38.92s/it]
Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84: 100%|██████████| 1/1 [00:38<00:00, 38.92s/it]
2025-07-14 18:36:40,493 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
selected lambda =  1000000.0
2025-07-14 18:37:30,378 [trainer.py] => No NME accuracy.
2025-07-14 18:37:30,381 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-14 18:37:30,381 [trainer.py] => CNN HM: [0.0]
2025-07-14 18:37:30,381 [trainer.py] => CNN top1 curve: [90.43]
Average Accuracy (CNN): 90.43
2025-07-14 18:37:30,381 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-14 18:37:30,383 [trainer.py] => All params: 87988289
2025-07-14 18:37:30,384 [trainer.py] => Trainable params: 1189633
2025-07-14 18:37:30,398 [ranpac.py] => Learning on 100-110
2025-07-14 18:37:33,361 [ranpac.py] => [KNN] task 1, fixed K=5
2025-07-14 18:37:35,373 [ranpac.py] => [KNN] task 1, weight sparsity: 0.950, distance_metric: cosine
/home/<USER>/workdir/FSCIL-Calibration-main/models/ranpac.py:113: RuntimeWarning: covariance is not positive-semidefinite.
  xx  = np.random.multivariate_normal(self.proto_list[class_index], self.cov_mats[class_index], 800)
2025-07-14 18:38:00,079 [trainer.py] => No NME accuracy.
2025-07-14 18:38:00,082 [trainer.py] => CNN: {'total': 89.63, '00-99': 89.88, '100-109': 87.16, 'old': 89.88, 'new': 87.16}
2025-07-14 18:38:00,082 [trainer.py] => CNN HM: [0.0, 88.499]
2025-07-14 18:38:00,083 [trainer.py] => CNN top1 curve: [90.43, 89.63]
Average Accuracy (CNN): 90.03
2025-07-14 18:38:00,083 [trainer.py] => Average Accuracy (CNN): 90.03 

2025-07-14 18:38:00,084 [trainer.py] => All params: 88088289
2025-07-14 18:38:00,085 [trainer.py] => Trainable params: 2289633
2025-07-14 18:38:00,098 [ranpac.py] => Learning on 110-120
2025-07-14 18:38:04,071 [ranpac.py] => [KNN] task 2, fixed K=5
2025-07-14 18:38:04,074 [ranpac.py] => [KNN] task 2, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:38:27,693 [trainer.py] => No NME accuracy.
2025-07-14 18:38:27,695 [trainer.py] => CNN: {'total': 87.76, '00-99': 89.29, '100-109': 88.18, '110-119': 71.83, 'old': 89.19, 'new': 71.83}
2025-07-14 18:38:27,696 [trainer.py] => CNN HM: [0.0, 88.499, 79.574]
2025-07-14 18:38:27,696 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76]
Average Accuracy (CNN): 89.27333333333333
2025-07-14 18:38:27,697 [trainer.py] => Average Accuracy (CNN): 89.27333333333333 

2025-07-14 18:38:27,698 [trainer.py] => All params: 88188289
2025-07-14 18:38:27,698 [trainer.py] => Trainable params: 2389633
2025-07-14 18:38:27,720 [ranpac.py] => Learning on 120-130
2025-07-14 18:38:31,258 [ranpac.py] => [KNN] task 3, fixed K=5
2025-07-14 18:38:31,261 [ranpac.py] => [KNN] task 3, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:38:56,463 [trainer.py] => No NME accuracy.
2025-07-14 18:38:56,465 [trainer.py] => CNN: {'total': 86.39, '00-99': 88.8, '100-109': 89.53, '110-119': 74.65, '120-129': 70.69, 'old': 87.71, 'new': 70.69}
2025-07-14 18:38:56,465 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 78.286]
2025-07-14 18:38:56,465 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.39]
Average Accuracy (CNN): 88.5525
2025-07-14 18:38:56,465 [trainer.py] => Average Accuracy (CNN): 88.5525 

2025-07-14 18:38:56,466 [trainer.py] => All params: 88288289
2025-07-14 18:38:56,467 [trainer.py] => Trainable params: 2489633
2025-07-14 18:38:56,483 [ranpac.py] => Learning on 130-140
2025-07-14 18:39:00,072 [ranpac.py] => [KNN] task 4, fixed K=5
2025-07-14 18:39:00,075 [ranpac.py] => [KNN] task 4, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:39:29,805 [trainer.py] => No NME accuracy.
2025-07-14 18:39:29,807 [trainer.py] => CNN: {'total': 85.71, '00-99': 88.46, '100-109': 89.86, '110-119': 74.3, '120-129': 68.62, '130-139': 82.41, 'old': 85.97, 'new': 82.41}
2025-07-14 18:39:29,807 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 78.286, 84.152]
2025-07-14 18:39:29,808 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.39, 85.71]
Average Accuracy (CNN): 87.984
2025-07-14 18:39:29,808 [trainer.py] => Average Accuracy (CNN): 87.984 

2025-07-14 18:39:29,810 [trainer.py] => All params: 88388289
2025-07-14 18:39:29,811 [trainer.py] => Trainable params: 2589633
2025-07-14 18:39:29,825 [ranpac.py] => Learning on 140-150
2025-07-14 18:39:33,376 [ranpac.py] => [KNN] task 5, fixed K=5
2025-07-14 18:39:33,377 [ranpac.py] => [KNN] task 5, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:40:04,047 [trainer.py] => No NME accuracy.
2025-07-14 18:40:04,049 [trainer.py] => CNN: {'total': 84.32, '00-99': 87.9, '100-109': 90.54, '110-119': 72.18, '120-129': 68.62, '130-139': 83.45, '140-149': 70.14, 'old': 85.29, 'new': 70.14}
2025-07-14 18:40:04,049 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 78.286, 84.152, 76.977]
2025-07-14 18:40:04,049 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.39, 85.71, 84.32]
Average Accuracy (CNN): 87.37333333333333
2025-07-14 18:40:04,049 [trainer.py] => Average Accuracy (CNN): 87.37333333333333 

2025-07-14 18:40:04,051 [trainer.py] => All params: 88488289
2025-07-14 18:40:04,052 [trainer.py] => Trainable params: 2689633
2025-07-14 18:40:04,068 [ranpac.py] => Learning on 150-160
2025-07-14 18:40:08,582 [ranpac.py] => [KNN] task 6, fixed K=5
2025-07-14 18:40:08,583 [ranpac.py] => [KNN] task 6, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:40:40,432 [trainer.py] => No NME accuracy.
2025-07-14 18:40:40,434 [trainer.py] => CNN: {'total': 83.88, '00-99': 87.42, '100-109': 88.18, '110-119': 73.59, '120-129': 69.66, '130-139': 83.79, '140-149': 70.5, '150-159': 81.51, 'old': 84.04, 'new': 81.51}
2025-07-14 18:40:40,435 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 78.286, 84.152, 76.977, 82.756]
2025-07-14 18:40:40,435 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.39, 85.71, 84.32, 83.88]
Average Accuracy (CNN): 86.87428571428572
2025-07-14 18:40:40,435 [trainer.py] => Average Accuracy (CNN): 86.87428571428572 

2025-07-14 18:40:40,437 [trainer.py] => All params: 88588289
2025-07-14 18:40:40,439 [trainer.py] => Trainable params: 2789633
2025-07-14 18:40:40,455 [ranpac.py] => Learning on 160-170
2025-07-14 18:40:43,152 [ranpac.py] => [KNN] task 7, fixed K=5
2025-07-14 18:40:43,154 [ranpac.py] => [KNN] task 7, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:41:15,640 [trainer.py] => No NME accuracy.
2025-07-14 18:41:15,643 [trainer.py] => CNN: {'total': 83.19, '00-99': 87.14, '100-109': 88.51, '110-119': 73.94, '120-129': 70.34, '130-139': 82.41, '140-149': 66.91, '150-159': 80.48, '160-169': 79.53, 'old': 83.42, 'new': 79.53}
2025-07-14 18:41:15,643 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 78.286, 84.152, 76.977, 82.756, 81.429]
2025-07-14 18:41:15,644 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.39, 85.71, 84.32, 83.88, 83.19]
Average Accuracy (CNN): 86.41375
2025-07-14 18:41:15,644 [trainer.py] => Average Accuracy (CNN): 86.41375 

2025-07-14 18:41:15,646 [trainer.py] => All params: 88688289
2025-07-14 18:41:15,649 [trainer.py] => Trainable params: 2889633
2025-07-14 18:41:15,667 [ranpac.py] => Learning on 170-180
2025-07-14 18:41:18,138 [ranpac.py] => [KNN] task 8, fixed K=5
2025-07-14 18:41:18,138 [ranpac.py] => [KNN] task 8, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:41:53,583 [trainer.py] => No NME accuracy.
2025-07-14 18:41:53,585 [trainer.py] => CNN: {'total': 82.33, '00-99': 86.2, '100-109': 88.18, '110-119': 73.94, '120-129': 70.34, '130-139': 81.72, '140-149': 65.11, '150-159': 80.82, '160-169': 79.19, '170-179': 79.87, 'old': 82.48, 'new': 79.87}
2025-07-14 18:41:53,585 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 78.286, 84.152, 76.977, 82.756, 81.429, 81.154]
2025-07-14 18:41:53,585 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.39, 85.71, 84.32, 83.88, 83.19, 82.33]
Average Accuracy (CNN): 85.96
2025-07-14 18:41:53,586 [trainer.py] => Average Accuracy (CNN): 85.96 

2025-07-14 18:41:53,587 [trainer.py] => All params: 88788289
2025-07-14 18:41:53,588 [trainer.py] => Trainable params: 2989633
2025-07-14 18:41:53,604 [ranpac.py] => Learning on 180-190
2025-07-14 18:41:56,974 [ranpac.py] => [KNN] task 9, fixed K=5
2025-07-14 18:41:56,977 [ranpac.py] => [KNN] task 9, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:42:25,802 [trainer.py] => No NME accuracy.
2025-07-14 18:42:25,804 [trainer.py] => CNN: {'total': 81.72, '00-99': 85.89, '100-109': 88.51, '110-119': 74.3, '120-129': 69.31, '130-139': 80.0, '140-149': 64.39, '150-159': 80.82, '160-169': 79.19, '170-179': 79.53, '180-189': 77.0, 'old': 81.98, 'new': 77.0}
2025-07-14 18:42:25,804 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 78.286, 84.152, 76.977, 82.756, 81.429, 81.154, 79.412]
2025-07-14 18:42:25,804 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.39, 85.71, 84.32, 83.88, 83.19, 82.33, 81.72]
Average Accuracy (CNN): 85.536
2025-07-14 18:42:25,804 [trainer.py] => Average Accuracy (CNN): 85.536 

2025-07-14 18:42:25,805 [trainer.py] => All params: 88888289
2025-07-14 18:42:25,806 [trainer.py] => Trainable params: 3089633
2025-07-14 18:42:25,824 [ranpac.py] => Learning on 190-200
2025-07-14 18:42:28,719 [ranpac.py] => [KNN] task 10, fixed K=5
2025-07-14 18:42:28,720 [ranpac.py] => [KNN] task 10, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:42:56,221 [trainer.py] => No NME accuracy.
2025-07-14 18:42:56,226 [trainer.py] => CNN: {'total': 80.81, '00-99': 84.89, '100-109': 89.19, '110-119': 73.59, '120-129': 68.62, '130-139': 81.03, '140-149': 65.11, '150-159': 80.82, '160-169': 79.19, '170-179': 79.19, '180-189': 75.96, '190-199': 73.99, 'old': 81.17, 'new': 73.99}
2025-07-14 18:42:56,226 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 78.286, 84.152, 76.977, 82.756, 81.429, 81.154, 79.412, 77.414]
2025-07-14 18:42:56,226 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.39, 85.71, 84.32, 83.88, 83.19, 82.33, 81.72, 80.81]
Average Accuracy (CNN): 85.10636363636364
2025-07-14 18:42:56,226 [trainer.py] => Average Accuracy (CNN): 85.10636363636364 

Accuracy Matrix (CNN):
[[90.43 89.88 89.29 88.8  88.46 87.9  87.42 87.14 86.2  85.89 84.89]
 [ 0.   87.16 88.18 89.53 89.86 90.54 88.18 88.51 88.18 88.51 89.19]
 [ 0.    0.   71.83 74.65 74.3  72.18 73.59 73.94 73.94 74.3  73.59]
 [ 0.    0.    0.   70.69 68.62 68.62 69.66 70.34 70.34 69.31 68.62]
 [ 0.    0.    0.    0.   82.41 83.45 83.79 82.41 81.72 80.   81.03]
 [ 0.    0.    0.    0.    0.   70.14 70.5  66.91 65.11 64.39 65.11]
 [ 0.    0.    0.    0.    0.    0.   81.51 80.48 80.82 80.82 80.82]
 [ 0.    0.    0.    0.    0.    0.    0.   79.53 79.19 79.19 79.19]
 [ 0.    0.    0.    0.    0.    0.    0.    0.   79.87 79.53 79.19]
 [ 0.    0.    0.    0.    0.    0.    0.    0.    0.   77.   75.96]
 [ 0.    0.    0.    0.    0.    0.    0.    0.    0.    0.   73.99]]
2025-07-14 18:42:56,227 [trainer.py] => Forgetting (CNN): 2.0920000000000045
