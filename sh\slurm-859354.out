2025-07-14 18:06:53,384 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/ranpac.json
2025-07-14 18:06:53,387 [trainer.py] => prefix: reproduce
2025-07-14 18:06:53,387 [trainer.py] => dataset: cub
2025-07-14 18:06:53,387 [trainer.py] => memory_size: 0
2025-07-14 18:06:53,387 [trainer.py] => shuffle: True
2025-07-14 18:06:53,387 [trainer.py] => init_cls: 100
2025-07-14 18:06:53,388 [trainer.py] => increment: 10
2025-07-14 18:06:53,388 [trainer.py] => model_name: ranpac
2025-07-14 18:06:53,388 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 18:06:53,388 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 18:06:53,388 [trainer.py] => seed: 1993
2025-07-14 18:06:53,388 [trainer.py] => resume: False
2025-07-14 18:06:53,388 [trainer.py] => shot: 5
2025-07-14 18:06:53,388 [trainer.py] => use_simplecil: False
2025-07-14 18:06:53,388 [trainer.py] => tuned_epoch: 40
2025-07-14 18:06:53,389 [trainer.py] => init_lr: 0.01
2025-07-14 18:06:53,389 [trainer.py] => batch_size: 48
2025-07-14 18:06:53,389 [trainer.py] => weight_decay: 0.0005
2025-07-14 18:06:53,389 [trainer.py] => min_lr: 0
2025-07-14 18:06:53,389 [trainer.py] => ffn_num: 64
2025-07-14 18:06:53,389 [trainer.py] => optimizer: sgd
2025-07-14 18:06:53,389 [trainer.py] => use_RP: True
2025-07-14 18:06:53,389 [trainer.py] => M: 10000
2025-07-14 18:06:53,389 [trainer.py] => fecam: False
2025-07-14 18:06:53,389 [trainer.py] => calibration: True
2025-07-14 18:06:53,389 [trainer.py] => knn_k: 5
2025-07-14 18:06:53,390 [trainer.py] => knn_distance_metric: cosine
2025-07-14 18:06:53,390 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 18:06:53,390 [trainer.py] => knn_adaptive_k: False
2025-07-14 18:06:53,390 [trainer.py] => knn_temperature: 16.0
2025-07-14 18:06:53,630 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
This is for the BaseNet initialization.
I'm using ViT with adapters.
_IncompatibleKeys(missing_keys=['blocks.0.adaptmlp.down_proj.weight', 'blocks.0.adaptmlp.down_proj.bias', 'blocks.0.adaptmlp.up_proj.weight', 'blocks.0.adaptmlp.up_proj.bias', 'blocks.1.adaptmlp.down_proj.weight', 'blocks.1.adaptmlp.down_proj.bias', 'blocks.1.adaptmlp.up_proj.weight', 'blocks.1.adaptmlp.up_proj.bias', 'blocks.2.adaptmlp.down_proj.weight', 'blocks.2.adaptmlp.down_proj.bias', 'blocks.2.adaptmlp.up_proj.weight', 'blocks.2.adaptmlp.up_proj.bias', 'blocks.3.adaptmlp.down_proj.weight', 'blocks.3.adaptmlp.down_proj.bias', 'blocks.3.adaptmlp.up_proj.weight', 'blocks.3.adaptmlp.up_proj.bias', 'blocks.4.adaptmlp.down_proj.weight', 'blocks.4.adaptmlp.down_proj.bias', 'blocks.4.adaptmlp.up_proj.weight', 'blocks.4.adaptmlp.up_proj.bias', 'blocks.5.adaptmlp.down_proj.weight', 'blocks.5.adaptmlp.down_proj.bias', 'blocks.5.adaptmlp.up_proj.weight', 'blocks.5.adaptmlp.up_proj.bias', 'blocks.6.adaptmlp.down_proj.weight', 'blocks.6.adaptmlp.down_proj.bias', 'blocks.6.adaptmlp.up_proj.weight', 'blocks.6.adaptmlp.up_proj.bias', 'blocks.7.adaptmlp.down_proj.weight', 'blocks.7.adaptmlp.down_proj.bias', 'blocks.7.adaptmlp.up_proj.weight', 'blocks.7.adaptmlp.up_proj.bias', 'blocks.8.adaptmlp.down_proj.weight', 'blocks.8.adaptmlp.down_proj.bias', 'blocks.8.adaptmlp.up_proj.weight', 'blocks.8.adaptmlp.up_proj.bias', 'blocks.9.adaptmlp.down_proj.weight', 'blocks.9.adaptmlp.down_proj.bias', 'blocks.9.adaptmlp.up_proj.weight', 'blocks.9.adaptmlp.up_proj.bias', 'blocks.10.adaptmlp.down_proj.weight', 'blocks.10.adaptmlp.down_proj.bias', 'blocks.10.adaptmlp.up_proj.weight', 'blocks.10.adaptmlp.up_proj.bias', 'blocks.11.adaptmlp.down_proj.weight', 'blocks.11.adaptmlp.down_proj.bias', 'blocks.11.adaptmlp.up_proj.weight', 'blocks.11.adaptmlp.up_proj.bias'], unexpected_keys=[])
After BaseNet initialization.
2025-07-14 18:07:30,257 [trainer.py] => All params: 86988288
2025-07-14 18:07:30,257 [trainer.py] => Trainable params: 1189632
2025-07-14 18:07:46,761 [ranpac.py] => Learning on 0-100
87,065,089 total parameters.
1,266,433 training parameters.
backbone.blocks.0.adaptmlp.down_proj.weight 49152
backbone.blocks.0.adaptmlp.down_proj.bias 64
backbone.blocks.0.adaptmlp.up_proj.weight 49152
backbone.blocks.0.adaptmlp.up_proj.bias 768
backbone.blocks.1.adaptmlp.down_proj.weight 49152
backbone.blocks.1.adaptmlp.down_proj.bias 64
backbone.blocks.1.adaptmlp.up_proj.weight 49152
backbone.blocks.1.adaptmlp.up_proj.bias 768
backbone.blocks.2.adaptmlp.down_proj.weight 49152
backbone.blocks.2.adaptmlp.down_proj.bias 64
backbone.blocks.2.adaptmlp.up_proj.weight 49152
backbone.blocks.2.adaptmlp.up_proj.bias 768
backbone.blocks.3.adaptmlp.down_proj.weight 49152
backbone.blocks.3.adaptmlp.down_proj.bias 64
backbone.blocks.3.adaptmlp.up_proj.weight 49152
backbone.blocks.3.adaptmlp.up_proj.bias 768
backbone.blocks.4.adaptmlp.down_proj.weight 49152
backbone.blocks.4.adaptmlp.down_proj.bias 64
backbone.blocks.4.adaptmlp.up_proj.weight 49152
backbone.blocks.4.adaptmlp.up_proj.bias 768
backbone.blocks.5.adaptmlp.down_proj.weight 49152
backbone.blocks.5.adaptmlp.down_proj.bias 64
backbone.blocks.5.adaptmlp.up_proj.weight 49152
backbone.blocks.5.adaptmlp.up_proj.bias 768
backbone.blocks.6.adaptmlp.down_proj.weight 49152
backbone.blocks.6.adaptmlp.down_proj.bias 64
backbone.blocks.6.adaptmlp.up_proj.weight 49152
backbone.blocks.6.adaptmlp.up_proj.bias 768
backbone.blocks.7.adaptmlp.down_proj.weight 49152
backbone.blocks.7.adaptmlp.down_proj.bias 64
backbone.blocks.7.adaptmlp.up_proj.weight 49152
backbone.blocks.7.adaptmlp.up_proj.bias 768
backbone.blocks.8.adaptmlp.down_proj.weight 49152
backbone.blocks.8.adaptmlp.down_proj.bias 64
backbone.blocks.8.adaptmlp.up_proj.weight 49152
backbone.blocks.8.adaptmlp.up_proj.bias 768
backbone.blocks.9.adaptmlp.down_proj.weight 49152
backbone.blocks.9.adaptmlp.down_proj.bias 64
backbone.blocks.9.adaptmlp.up_proj.weight 49152
backbone.blocks.9.adaptmlp.up_proj.bias 768
backbone.blocks.10.adaptmlp.down_proj.weight 49152
backbone.blocks.10.adaptmlp.down_proj.bias 64
backbone.blocks.10.adaptmlp.up_proj.weight 49152
backbone.blocks.10.adaptmlp.up_proj.bias 768
backbone.blocks.11.adaptmlp.down_proj.weight 49152
backbone.blocks.11.adaptmlp.down_proj.bias 64
backbone.blocks.11.adaptmlp.up_proj.weight 49152
backbone.blocks.11.adaptmlp.up_proj.bias 768
fc.weight 76800
fc.sigma 1

  0%|          | 0/40 [00:00<?, ?it/s]
Task 0, Epoch 1/40 => Loss 4.586, Train_accy 4.84, Test_accy 14.84:   0%|          | 0/40 [01:01<?, ?it/s]
Task 0, Epoch 1/40 => Loss 4.586, Train_accy 4.84, Test_accy 14.84:   2%|▎         | 1/40 [01:01<39:50, 61.30s/it]
Task 0, Epoch 2/40 => Loss 4.519, Train_accy 23.96, Test_accy 43.64:   2%|▎         | 1/40 [01:38<39:50, 61.30s/it]
Task 0, Epoch 2/40 => Loss 4.519, Train_accy 23.96, Test_accy 43.64:   5%|▌         | 2/40 [01:38<29:50, 47.12s/it]
Task 0, Epoch 3/40 => Loss 4.366, Train_accy 45.91, Test_accy 63.67:   5%|▌         | 2/40 [02:10<29:50, 47.12s/it]
Task 0, Epoch 3/40 => Loss 4.366, Train_accy 45.91, Test_accy 63.67:   8%|▊         | 3/40 [02:10<24:44, 40.12s/it]
Task 0, Epoch 4/40 => Loss 3.998, Train_accy 61.86, Test_accy 74.80:   8%|▊         | 3/40 [02:42<24:44, 40.12s/it]
Task 0, Epoch 4/40 => Loss 3.998, Train_accy 61.86, Test_accy 74.80:  10%|█         | 4/40 [02:42<22:07, 36.88s/it]
Task 0, Epoch 5/40 => Loss 3.248, Train_accy 71.81, Test_accy 81.01:  10%|█         | 4/40 [03:12<22:07, 36.88s/it]
Task 0, Epoch 5/40 => Loss 3.248, Train_accy 71.81, Test_accy 81.01:  12%|█▎        | 5/40 [03:12<20:08, 34.51s/it]
Task 0, Epoch 6/40 => Loss 2.086, Train_accy 77.68, Test_accy 86.14:  12%|█▎        | 5/40 [03:38<20:08, 34.51s/it]
Task 0, Epoch 6/40 => Loss 2.086, Train_accy 77.68, Test_accy 86.14:  15%|█▌        | 6/40 [03:38<17:53, 31.58s/it]
Task 0, Epoch 7/40 => Loss 1.318, Train_accy 82.12, Test_accy 88.25:  15%|█▌        | 6/40 [04:11<17:53, 31.58s/it]
Task 0, Epoch 7/40 => Loss 1.318, Train_accy 82.12, Test_accy 88.25:  18%|█▊        | 7/40 [04:11<17:35, 31.98s/it]
Task 0, Epoch 8/40 => Loss 0.986, Train_accy 84.75, Test_accy 89.12:  18%|█▊        | 7/40 [04:40<17:35, 31.98s/it]
Task 0, Epoch 8/40 => Loss 0.986, Train_accy 84.75, Test_accy 89.12:  20%|██        | 8/40 [04:40<16:40, 31.26s/it]
Task 0, Epoch 9/40 => Loss 0.848, Train_accy 85.39, Test_accy 89.95:  20%|██        | 8/40 [05:06<16:40, 31.26s/it]
Task 0, Epoch 9/40 => Loss 0.848, Train_accy 85.39, Test_accy 89.95:  22%|██▎       | 9/40 [05:06<15:15, 29.53s/it]
Task 0, Epoch 10/40 => Loss 0.739, Train_accy 87.12, Test_accy 89.84:  22%|██▎       | 9/40 [05:33<15:15, 29.53s/it]
Task 0, Epoch 10/40 => Loss 0.739, Train_accy 87.12, Test_accy 89.84:  25%|██▌       | 10/40 [05:33<14:22, 28.77s/it]
Task 0, Epoch 11/40 => Loss 0.668, Train_accy 87.65, Test_accy 90.61:  25%|██▌       | 10/40 [06:01<14:22, 28.77s/it]
Task 0, Epoch 11/40 => Loss 0.668, Train_accy 87.65, Test_accy 90.61:  28%|██▊       | 11/40 [06:01<13:45, 28.45s/it]
Task 0, Epoch 12/40 => Loss 0.636, Train_accy 87.45, Test_accy 91.61:  28%|██▊       | 11/40 [06:31<13:45, 28.45s/it]
Task 0, Epoch 12/40 => Loss 0.636, Train_accy 87.45, Test_accy 91.61:  30%|███       | 12/40 [06:31<13:26, 28.81s/it]
Task 0, Epoch 13/40 => Loss 0.620, Train_accy 88.02, Test_accy 91.47:  30%|███       | 12/40 [07:04<13:26, 28.81s/it]
Task 0, Epoch 13/40 => Loss 0.620, Train_accy 88.02, Test_accy 91.47:  32%|███▎      | 13/40 [07:04<13:38, 30.32s/it]
Task 0, Epoch 14/40 => Loss 0.569, Train_accy 89.52, Test_accy 91.68:  32%|███▎      | 13/40 [07:34<13:38, 30.32s/it]
Task 0, Epoch 14/40 => Loss 0.569, Train_accy 89.52, Test_accy 91.68:  35%|███▌      | 14/40 [07:34<13:05, 30.20s/it]
Task 0, Epoch 15/40 => Loss 0.540, Train_accy 89.79, Test_accy 91.75:  35%|███▌      | 14/40 [08:02<13:05, 30.20s/it]
Task 0, Epoch 15/40 => Loss 0.540, Train_accy 89.79, Test_accy 91.75:  38%|███▊      | 15/40 [08:02<12:15, 29.43s/it]
Task 0, Epoch 16/40 => Loss 0.547, Train_accy 89.09, Test_accy 91.96:  38%|███▊      | 15/40 [08:33<12:15, 29.43s/it]
Task 0, Epoch 16/40 => Loss 0.547, Train_accy 89.09, Test_accy 91.96:  40%|████      | 16/40 [08:33<11:58, 29.96s/it]
Task 0, Epoch 17/40 => Loss 0.530, Train_accy 89.22, Test_accy 91.96:  40%|████      | 16/40 [09:01<11:58, 29.96s/it]
Task 0, Epoch 17/40 => Loss 0.530, Train_accy 89.22, Test_accy 91.96:  42%|████▎     | 17/40 [09:01<11:13, 29.30s/it]
Task 0, Epoch 18/40 => Loss 0.488, Train_accy 90.36, Test_accy 92.27:  42%|████▎     | 17/40 [09:32<11:13, 29.30s/it]
Task 0, Epoch 18/40 => Loss 0.488, Train_accy 90.36, Test_accy 92.27:  45%|████▌     | 18/40 [09:32<10:58, 29.95s/it]
Task 0, Epoch 19/40 => Loss 0.531, Train_accy 89.06, Test_accy 92.20:  45%|████▌     | 18/40 [10:03<10:58, 29.95s/it]
Task 0, Epoch 19/40 => Loss 0.531, Train_accy 89.06, Test_accy 92.20:  48%|████▊     | 19/40 [10:03<10:34, 30.20s/it]
Task 0, Epoch 20/40 => Loss 0.486, Train_accy 90.06, Test_accy 92.03:  48%|████▊     | 19/40 [10:39<10:34, 30.20s/it]
Task 0, Epoch 20/40 => Loss 0.486, Train_accy 90.06, Test_accy 92.03:  50%|█████     | 20/40 [10:39<10:37, 31.88s/it]
Task 0, Epoch 21/40 => Loss 0.443, Train_accy 91.79, Test_accy 92.10:  50%|█████     | 20/40 [11:11<10:37, 31.88s/it]
Task 0, Epoch 21/40 => Loss 0.443, Train_accy 91.79, Test_accy 92.10:  52%|█████▎    | 21/40 [11:11<10:06, 31.91s/it]
Task 0, Epoch 22/40 => Loss 0.431, Train_accy 91.62, Test_accy 92.44:  52%|█████▎    | 21/40 [11:42<10:06, 31.91s/it]
Task 0, Epoch 22/40 => Loss 0.431, Train_accy 91.62, Test_accy 92.44:  55%|█████▌    | 22/40 [11:42<09:28, 31.56s/it]
Task 0, Epoch 23/40 => Loss 0.443, Train_accy 91.26, Test_accy 92.37:  55%|█████▌    | 22/40 [12:16<09:28, 31.56s/it]
Task 0, Epoch 23/40 => Loss 0.443, Train_accy 91.26, Test_accy 92.37:  57%|█████▊    | 23/40 [12:16<09:11, 32.45s/it]
Task 0, Epoch 24/40 => Loss 0.442, Train_accy 91.59, Test_accy 92.37:  57%|█████▊    | 23/40 [12:51<09:11, 32.45s/it]
Task 0, Epoch 24/40 => Loss 0.442, Train_accy 91.59, Test_accy 92.37:  60%|██████    | 24/40 [12:51<08:52, 33.26s/it]
Task 0, Epoch 25/40 => Loss 0.433, Train_accy 91.12, Test_accy 92.44:  60%|██████    | 24/40 [13:24<08:52, 33.26s/it]
Task 0, Epoch 25/40 => Loss 0.433, Train_accy 91.12, Test_accy 92.44:  62%|██████▎   | 25/40 [13:24<08:17, 33.18s/it]
Task 0, Epoch 26/40 => Loss 0.421, Train_accy 91.83, Test_accy 92.37:  62%|██████▎   | 25/40 [13:52<08:17, 33.18s/it]
Task 0, Epoch 26/40 => Loss 0.421, Train_accy 91.83, Test_accy 92.37:  65%|██████▌   | 26/40 [13:52<07:20, 31.48s/it]
Task 0, Epoch 27/40 => Loss 0.417, Train_accy 92.46, Test_accy 92.17:  65%|██████▌   | 26/40 [14:18<07:20, 31.48s/it]
Task 0, Epoch 27/40 => Loss 0.417, Train_accy 92.46, Test_accy 92.17:  68%|██████▊   | 27/40 [14:18<06:27, 29.83s/it]
Task 0, Epoch 28/40 => Loss 0.409, Train_accy 91.52, Test_accy 92.34:  68%|██████▊   | 27/40 [14:46<06:27, 29.83s/it]
Task 0, Epoch 28/40 => Loss 0.409, Train_accy 91.52, Test_accy 92.34:  70%|███████   | 28/40 [14:46<05:50, 29.19s/it]
Task 0, Epoch 29/40 => Loss 0.397, Train_accy 91.89, Test_accy 92.31:  70%|███████   | 28/40 [15:13<05:50, 29.19s/it]
Task 0, Epoch 29/40 => Loss 0.397, Train_accy 91.89, Test_accy 92.31:  72%|███████▎  | 29/40 [15:13<05:15, 28.73s/it]
Task 0, Epoch 30/40 => Loss 0.417, Train_accy 91.79, Test_accy 92.34:  72%|███████▎  | 29/40 [15:46<05:15, 28.73s/it]
Task 0, Epoch 30/40 => Loss 0.417, Train_accy 91.79, Test_accy 92.34:  75%|███████▌  | 30/40 [15:46<04:58, 29.88s/it]
Task 0, Epoch 31/40 => Loss 0.400, Train_accy 92.43, Test_accy 92.34:  75%|███████▌  | 30/40 [16:16<04:58, 29.88s/it]
Task 0, Epoch 31/40 => Loss 0.400, Train_accy 92.43, Test_accy 92.34:  78%|███████▊  | 31/40 [16:16<04:30, 30.05s/it]
Task 0, Epoch 32/40 => Loss 0.408, Train_accy 92.26, Test_accy 92.41:  78%|███████▊  | 31/40 [16:48<04:30, 30.05s/it]
Task 0, Epoch 32/40 => Loss 0.408, Train_accy 92.26, Test_accy 92.41:  80%|████████  | 32/40 [16:48<04:04, 30.53s/it]
Task 0, Epoch 33/40 => Loss 0.405, Train_accy 92.43, Test_accy 92.44:  80%|████████  | 32/40 [17:16<04:04, 30.53s/it]
Task 0, Epoch 33/40 => Loss 0.405, Train_accy 92.43, Test_accy 92.44:  82%|████████▎ | 33/40 [17:16<03:28, 29.79s/it]
Task 0, Epoch 34/40 => Loss 0.408, Train_accy 92.09, Test_accy 92.48:  82%|████████▎ | 33/40 [17:44<03:28, 29.79s/it]
Task 0, Epoch 34/40 => Loss 0.408, Train_accy 92.09, Test_accy 92.48:  85%|████████▌ | 34/40 [17:44<02:54, 29.15s/it]
Task 0, Epoch 35/40 => Loss 0.389, Train_accy 92.46, Test_accy 92.55:  85%|████████▌ | 34/40 [18:14<02:54, 29.15s/it]
Task 0, Epoch 35/40 => Loss 0.389, Train_accy 92.46, Test_accy 92.55:  88%|████████▊ | 35/40 [18:14<02:28, 29.67s/it]
Task 0, Epoch 36/40 => Loss 0.406, Train_accy 91.89, Test_accy 92.48:  88%|████████▊ | 35/40 [18:38<02:28, 29.67s/it]
Task 0, Epoch 36/40 => Loss 0.406, Train_accy 91.89, Test_accy 92.48:  90%|█████████ | 36/40 [18:38<01:51, 27.98s/it]
Task 0, Epoch 37/40 => Loss 0.403, Train_accy 91.89, Test_accy 92.44:  90%|█████████ | 36/40 [19:06<01:51, 27.98s/it]
Task 0, Epoch 37/40 => Loss 0.403, Train_accy 91.89, Test_accy 92.44:  92%|█████████▎| 37/40 [19:06<01:23, 27.93s/it]
Task 0, Epoch 38/40 => Loss 0.411, Train_accy 92.09, Test_accy 92.48:  92%|█████████▎| 37/40 [19:35<01:23, 27.93s/it]
Task 0, Epoch 38/40 => Loss 0.411, Train_accy 92.09, Test_accy 92.48:  95%|█████████▌| 38/40 [19:35<00:56, 28.02s/it]
Task 0, Epoch 39/40 => Loss 0.394, Train_accy 92.09, Test_accy 92.48:  95%|█████████▌| 38/40 [19:59<00:56, 28.02s/it]
Task 0, Epoch 39/40 => Loss 0.394, Train_accy 92.09, Test_accy 92.48:  98%|█████████▊| 39/40 [19:59<00:26, 26.93s/it]
Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48:  98%|█████████▊| 39/40 [20:23<00:26, 26.93s/it]
Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48: 100%|██████████| 40/40 [20:23<00:00, 26.12s/it]
Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48: 100%|██████████| 40/40 [20:23<00:00, 30.59s/it]
2025-07-14 18:28:10,436 [ranpac.py] => Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48
Traceback (most recent call last):
  File "/home/<USER>/workdir/FSCIL-Calibration-main/main.py", line 25, in <module>
    main()
  File "/home/<USER>/workdir/FSCIL-Calibration-main/main.py", line 11, in main
    train(args)
  File "/home/<USER>/workdir/FSCIL-Calibration-main/trainer.py", line 19, in train
    _train(args)
  File "/home/<USER>/workdir/FSCIL-Calibration-main/trainer.py", line 74, in _train
    model.incremental_train(data_manager)
  File "/home/<USER>/workdir/FSCIL-Calibration-main/models/ranpac.py", line 325, in incremental_train
    self._train(self.train_loader, self.test_loader, self.train_loader_for_protonet)
  File "/home/<USER>/workdir/FSCIL-Calibration-main/models/ranpac.py", line 351, in _train
    self.save_checkpoint("weights/{}_{}_{}_{}".format(self.args["dataset"],self.args["model_name"],self.args["init_cls"],self.args["increment"]))
  File "/home/<USER>/workdir/FSCIL-Calibration-main/models/base.py", line 42, in save_checkpoint
    torch.save(save_dict, "{}_{}.pkl".format(filename, self._cur_task))
  File "/home/<USER>/anaconda3/envs/CAL/lib/python3.9/site-packages/torch/serialization.py", line 422, in save
    with _open_zipfile_writer(f) as opened_zipfile:
  File "/home/<USER>/anaconda3/envs/CAL/lib/python3.9/site-packages/torch/serialization.py", line 309, in _open_zipfile_writer
    return container(name_or_buffer)
  File "/home/<USER>/anaconda3/envs/CAL/lib/python3.9/site-packages/torch/serialization.py", line 287, in __init__
    super(_open_zipfile_writer_file, self).__init__(torch._C.PyTorchFileWriter(str(name)))
RuntimeError: Parent directory weights does not exist.
