import logging
import numpy as np
from scipy.linalg import inv, det
import torch
import pickle
from torch import nn
from torch.serialization import load
from tqdm import tqdm
from torch import optim
from torch.nn import functional as F
from torch.utils.data import DataLoader
from utils.inc_net import IncrementalNet,SimpleCosineIncrementalNet,MultiBranchCosineIncrementalNet,SimpleVitNet
from models.base import BaseLearner
from utils.toolkit import target2onehot, tensor2numpy


# Tune the model at first session with adapter, and then classify with NCM/TEEN/FeCAM.
# To run TEEN, set fecam to false and calibartion to true
# To run NCM, set calibration and fecam to false

num_workers = 8


class Learner(BaseLearner):
    def __init__(self, args):
        super().__init__(args)
        if 'adapter' not in args["backbone_type"]:
            raise NotImplementedError('Adapter requires Adapter backbone')

        if 'resnet' in args['backbone_type']:
            self._network = SimpleCosineIncrementalNet(args, True)
            self. batch_size=128
            self.init_lr=args["init_lr"] if args["init_lr"] is not None else  0.01
        else:
            if self.args['resume']:
                self._network = SimpleVitNet(args, False)
            else:
                self._network = SimpleVitNet(args, True)
            self. batch_size= args["batch_size"]
            self. init_lr=args["init_lr"]
        
        self.weight_decay=args["weight_decay"] if args["weight_decay"] is not None else 0.0005
        self.min_lr=args['min_lr'] if args['min_lr'] is not None else 1e-8
        self.args=args
        self.cov_mats, self.base_cov_mats = [], []
        # KNN calibration parameters
        self.knn_k = args.get("knn_k", 5)  # Number of nearest base prototypes to use during calibration
        self.knn_distance_metric = args.get("knn_distance_metric", "cosine")  # cosine, euclidean, mahalanobis
        self.knn_weight_decay = args.get("knn_weight_decay", 0.1)  # Weight decay factor based on distance
        self.knn_adaptive_k = args.get("knn_adaptive_k", False)  # Whether to use adaptive K selection
        self.knn_temperature = args.get("knn_temperature", 16.0)  # Temperature for similarity scaling

        # Dynamic K value parameters
        self.k_min = args.get("k_min", 3)  # Minimum K value for dynamic selection
        self.k_max = args.get("k_max", 21)  # Maximum K value for dynamic selection
        self.dynamic_k_method = args.get("dynamic_k_method", "cosine_similarity")  # Method for dynamic K selection
        self.cosine_temperature = args.get("cosine_temperature", 16.0)  # Temperature for scaled cosine similarity

        # Storage for base class similarity statistics
        self.base_similarity_stats = {
            'sim_min': None,
            'sim_max': None,
            'computed': False
        }
        self.ridge = 1
        self.beta = 0.5

    def after_task(self):
        self._known_classes = self._total_classes

        # 如果是base task完成，预计算基类相似度统计
        if (self._cur_task == 0 and self.dynamic_k_method == "cosine_similarity"):
            try:
                # 从网络权重中获取基类原型
                base_protos = self._network.fc.weight.data[:self.args['init_cls']].detach().cpu()
                self._compute_base_similarity_stats(base_protos)
                logging.info(f'[Dynamic-K] Base similarity stats computed after base task')
            except Exception as e:
                logging.warning(f'[Dynamic-K] Failed to compute base similarity stats: {e}')
    
    def replace_fc(self, trainloader, model, args):
        # replace fc.weight with the embedding average of train data
        model = model.eval()
        embedding_list = []
        label_list = []
        with torch.no_grad():
            for i, batch in enumerate(trainloader):
                (_,data,label)=batch
                data=data.to(self._device)
                label=label.to(self._device)
                embedding = model(data)['features']
                embedding_list.append(embedding.cpu())
                label_list.append(label.cpu())
        embedding_list = torch.cat(embedding_list, dim=0)
        label_list = torch.cat(label_list, dim=0)

        class_list=np.unique(self.train_dataset.labels)
        proto_list = []

        for class_index in class_list:
            data_index=(label_list==class_index).nonzero().squeeze(-1)
            embedding=embedding_list[data_index]
            proto=embedding.mean(0)
            proto_list.append(proto)
            
            if args['fecam']:
                cov = torch.cov(embedding.T)
                self.cov_mats.append(cov)
                if self._cur_task == 0:
                    self.base_cov_mats.append(cov)
                    
            if not args['calibration'] or self._cur_task == 0:
                self._network.fc.weight.data[class_index] = proto
        
        base_protos = self._network.fc.weight.data[:args['init_cls']].detach().cpu()
        proto_list = torch.stack(proto_list).detach().cpu()

        # Use enhanced KNN-based weighting for calibration
        norm_weights = self._compute_knn_weights(proto_list, base_protos, args['init_cls'])
        
        if args['calibration'] and self._cur_task != 0:
            alpha = 0.9  # 0.9 for CUB200, Aircrafts, Cars and 0.75 for CIFAR100
            delta_protos = torch.matmul(norm_weights, base_protos)
            delta_protos = F.normalize(delta_protos, p=2, dim=-1)
            updated_protos = alpha * cur_protos + (1-alpha) * delta_protos
            for idd, class_index in enumerate(class_list):
                self._network.fc.weight.data[class_index] = updated_protos[idd]

        if args['fecam']:
            if not args['calibration'] or self._cur_task == 0:
                self.ridge = 100
                for idd, class_index in enumerate(class_list):
                    self.cov_mats[class_index] = torch.corrcoef(self.shrink_cov(self.cov_mats[class_index],self.ridge))
            else:
                # Enhanced KNN-based covariance calibration
                beta = 1.0
                for idd, class_index in enumerate(class_list):
                    # Use KNN weights for covariance matrix calibration
                    knn_weights = norm_weights[idd]  # [init_cls]

                    # Only use non-zero weights (selected neighbors)
                    nonzero_mask = knn_weights > 0
                    selected_weights = knn_weights[nonzero_mask]
                    selected_base_covs = torch.stack([self.base_cov_mats[i] for i in range(len(self.base_cov_mats)) if nonzero_mask[i]], 0)

                    if len(selected_base_covs) > 0:
                        # Weighted combination of selected base covariance matrices
                        delta_covs = selected_weights.view(-1, 1, 1) * selected_base_covs
                        weighted_base_cov = torch.sum(delta_covs, 0)

                        # Combine with current covariance matrix
                        self.cov_mats[class_index] = beta * weighted_base_cov + beta * self.cov_mats[class_index]

                        logging.debug(f'[KNN-FeCAM] class {class_index}: used {len(selected_base_covs)} base covariances, '
                                    f'weights sum: {selected_weights.sum():.3f}')
                    else:
                        # Fallback to original method if no neighbors selected
                        delta_covs = norm_weights[idd].view(args['init_cls'], 1, 1)*torch.stack(self.base_cov_mats[:args['init_cls']],0)
                        self.cov_mats[class_index] = beta*torch.sum(delta_covs,0) + beta*self.cov_mats[class_index]

                    self.cov_mats[class_index] = torch.corrcoef(self.shrink_cov(self.cov_mats[class_index],self.ridge))

        return model

    def _compute_similarity_matrix(self, query_protos, base_protos):
        """
        Compute similarity matrix between query prototypes and base prototypes
        Args:
            query_protos: [n_new, feat_dim] - new class prototypes
            base_protos: [init_cls, feat_dim] - base class prototypes
        Returns:
            similarity_matrix: [n_new, init_cls] - similarity scores
        """
        if self.knn_distance_metric == "cosine":
            # Normalize prototypes for cosine similarity
            query_norm = F.normalize(query_protos, p=2, dim=-1)
            base_norm = F.normalize(base_protos, p=2, dim=-1)
            sim_matrix = torch.mm(query_norm, base_norm.T) * self.knn_temperature
        elif self.knn_distance_metric == "euclidean":
            # Compute negative euclidean distance (higher is more similar)
            query_expanded = query_protos.unsqueeze(1)  # [n_new, 1, feat_dim]
            base_expanded = base_protos.unsqueeze(0)    # [1, init_cls, feat_dim]
            euclidean_dist = torch.norm(query_expanded - base_expanded, dim=2)  # [n_new, init_cls]
            sim_matrix = -euclidean_dist * self.knn_temperature
        elif self.knn_distance_metric == "mahalanobis":
            # Use base covariance matrices for Mahalanobis distance
            sim_matrix = self._compute_mahalanobis_similarity(query_protos, base_protos)
        else:
            raise ValueError(f"Unsupported distance metric: {self.knn_distance_metric}")

        return sim_matrix

    def _compute_mahalanobis_similarity(self, query_protos, base_protos):
        """Compute Mahalanobis-based similarity using base class covariance matrices"""
        n_new, feat_dim = query_protos.shape
        init_cls = base_protos.shape[0]
        sim_matrix = torch.zeros(n_new, init_cls)

        for i, query_proto in enumerate(query_protos):
            for j, base_proto in enumerate(base_protos):
                if j < len(self.base_cov_mats):
                    # Use base class covariance matrix
                    cov_inv = torch.linalg.pinv(self.base_cov_mats[j]).float()
                    diff = query_proto - base_proto
                    mahal_dist = torch.sqrt(torch.matmul(torch.matmul(diff, cov_inv), diff))
                    sim_matrix[i, j] = -mahal_dist * self.knn_temperature
                else:
                    # Fallback to euclidean distance
                    euclidean_dist = torch.norm(query_proto - base_proto)
                    sim_matrix[i, j] = -euclidean_dist * self.knn_temperature

        return sim_matrix

    def _adaptive_k_selection(self, similarity_matrix, base_k):
        """
        Adaptively select K based on similarity distribution
        Args:
            similarity_matrix: [n_new, init_cls] - similarity scores
            base_k: base K value from configuration
        Returns:
            k_values: [n_new] - adaptive K for each new class
        """
        n_new, init_cls = similarity_matrix.shape
        k_values = []

        for i in range(n_new):
            sim_scores = similarity_matrix[i]

            # Method 1: Use similarity threshold
            sorted_sims, _ = torch.sort(sim_scores, descending=True)
            if len(sorted_sims) > 1:
                # Find elbow point in similarity scores
                diffs = sorted_sims[:-1] - sorted_sims[1:]
                if len(diffs) > 0:
                    elbow_idx = torch.argmax(diffs).item() + 1
                    adaptive_k = min(max(elbow_idx, 1), base_k, init_cls)
                else:
                    adaptive_k = min(base_k, init_cls)
            else:
                adaptive_k = min(base_k, init_cls)

            k_values.append(adaptive_k)

        return k_values

    def _compute_knn_weights(self, cur_proto_list, base_protos, init_cls):
        """
        Enhanced KNN-based weight computation with dynamic K selection
        Args:
            cur_proto_list: [n_new, feat_dim] - current task prototypes
            base_protos: [init_cls, feat_dim] - base class prototypes
            init_cls: number of base classes
        Returns:
            norm_weights: [n_new, init_cls] - normalized sparse weights
        """
        n_new = cur_proto_list.shape[0]

        # Compute similarity matrix using selected distance metric
        if self.knn_distance_metric == "cosine" and self.dynamic_k_method == "cosine_similarity":
            # Use scaled cosine similarity for dynamic K method
            sim_matrix = self._compute_scaled_cosine_similarity(cur_proto_list, base_protos)
        else:
            # Use original similarity computation
            sim_matrix = self._compute_similarity_matrix(cur_proto_list, base_protos)

        # Dynamic/Adaptive K selection
        if self.knn_adaptive_k and self.dynamic_k_method == "cosine_similarity":
            # Use dynamic K based on cosine similarity
            k_values = self._compute_dynamic_k_values(cur_proto_list, base_protos)
            logging.info(f'[KNN] task {self._cur_task}, dynamic K values: {k_values}')
        elif self.knn_adaptive_k:
            # Use original adaptive K selection
            k_values = self._adaptive_k_selection(sim_matrix, self.knn_k)
            logging.info(f'[KNN] task {self._cur_task}, adaptive K values: {k_values}')
        else:
            # Use fixed K value
            k_values = [min(self.knn_k, init_cls)] * n_new
            logging.info(f'[KNN] task {self._cur_task}, fixed K={k_values[0]}')

        # Initialize sparse weight matrix
        norm_weights = torch.zeros(n_new, init_cls)

        for i in range(n_new):
            K = k_values[i]
            sim_scores = sim_matrix[i]

            if K < init_cls:
                # Select top-K most similar base prototypes
                topk_val, topk_idx = torch.topk(sim_scores, K, dim=0)

                # Apply distance-based weight decay
                if self.knn_weight_decay > 0:
                    # Convert similarities to distances (assuming higher similarity = lower distance)
                    max_sim = topk_val.max()
                    distances = max_sim - topk_val
                    decay_weights = torch.exp(-self.knn_weight_decay * distances)
                    topk_val = topk_val * decay_weights

                # Compute softmax weights over top-K neighbors
                weights_k = torch.softmax(topk_val, dim=0)

                # Assign weights to corresponding positions
                norm_weights[i].scatter_(0, topk_idx, weights_k)

                logging.debug(f'[KNN] class {i}: selected neighbors {topk_idx.tolist()}, '
                            f'weights {weights_k.tolist()[:3]}...')
            else:
                # Fallback to dense softmax when K >= #base classes
                norm_weights[i] = torch.softmax(sim_scores, dim=0)
                logging.debug(f'[KNN] class {i}: using dense weights (K={K} >= init_cls={init_cls})')

        # Log sparsity statistics
        sparsity = (norm_weights == 0).float().mean().item()
        logging.info(f'[KNN] task {self._cur_task}, weight sparsity: {sparsity:.3f}, '
                    f'distance_metric: {self.knn_distance_metric}')

        return norm_weights

    def _compute_scaled_cosine_similarity(self, query_protos, base_protos):
        """
        计算缩放余弦相似度: S = (μ_b · μ_n) / (||μ_b|| · ||μ_n||) · τ
        Args:
            query_protos: [n_query, feat_dim] - 查询原型
            base_protos: [n_base, feat_dim] - 基类原型
        Returns:
            scaled_sim: [n_query, n_base] - 缩放余弦相似度矩阵
        """
        # 归一化向量
        query_norm = F.normalize(query_protos, p=2, dim=-1)
        base_norm = F.normalize(base_protos, p=2, dim=-1)

        # 计算余弦相似度
        cosine_sim = torch.mm(query_norm, base_norm.T)

        # 应用温度缩放
        scaled_sim = cosine_sim * self.cosine_temperature

        return scaled_sim

    def _compute_base_similarity_stats(self, base_protos):
        """
        预计算基类间相似度统计信息
        Args:
            base_protos: [init_cls, feat_dim] - 基类原型
        """
        init_cls = base_protos.shape[0]
        avg_similarities = []

        logging.info(f'[Dynamic-K] Computing base similarity stats for {init_cls} base classes')

        # 计算每个基类与其他基类的平均相似度
        for i in range(init_cls):
            similarities = []
            for j in range(init_cls):
                if i != j:
                    # 使用缩放余弦相似度
                    sim = self._compute_scaled_cosine_similarity(
                        base_protos[i:i+1], base_protos[j:j+1]
                    )[0, 0]
                    similarities.append(sim.item())

            if len(similarities) > 0:
                avg_sim = sum(similarities) / len(similarities)
                avg_similarities.append(avg_sim)

        if len(avg_similarities) > 0:
            # 确定范围
            self.base_similarity_stats['sim_min'] = min(avg_similarities)
            self.base_similarity_stats['sim_max'] = max(avg_similarities)
            self.base_similarity_stats['computed'] = True

            logging.info(f'[Dynamic-K] Base similarity range: '
                        f'[{self.base_similarity_stats["sim_min"]:.4f}, '
                        f'{self.base_similarity_stats["sim_max"]:.4f}]')
        else:
            logging.warning('[Dynamic-K] No base similarities computed, using default range')
            self.base_similarity_stats['sim_min'] = 0.0
            self.base_similarity_stats['sim_max'] = self.cosine_temperature
            self.base_similarity_stats['computed'] = True

    def _compute_dynamic_k_values(self, cur_proto_list, base_protos):
        """
        基于余弦相似度计算动态K值
        Args:
            cur_proto_list: [n_new, feat_dim] - 新类原型
            base_protos: [init_cls, feat_dim] - 基类原型
        Returns:
            k_values: [n_new] - 每个新类的动态K值
        """
        if not self.base_similarity_stats['computed']:
            # 如果还没有预计算，先计算基类相似度统计
            self._compute_base_similarity_stats(base_protos)

        n_new = cur_proto_list.shape[0]
        init_cls = base_protos.shape[0]
        k_values = []

        sim_min = self.base_similarity_stats['sim_min']
        sim_max = self.base_similarity_stats['sim_max']

        logging.info(f'[Dynamic-K] Computing dynamic K for {n_new} new classes')

        for i in range(n_new):
            # 计算新类与所有基类的缩放余弦相似度
            similarities = self._compute_scaled_cosine_similarity(
                cur_proto_list[i:i+1], base_protos
            )[0]  # [init_cls]

            # 计算平均相似度
            avg_similarity = similarities.mean().item()

            # 归一化到[0,1]
            if sim_max > sim_min:
                normalized_similarity = (avg_similarity - sim_min) / (sim_max - sim_min)
                # 确保在[0,1]范围内
                normalized_similarity = max(0.0, min(1.0, normalized_similarity))
            else:
                # 如果范围为0，使用中间值
                normalized_similarity = 0.5
                logging.warning(f'[Dynamic-K] sim_max == sim_min, using normalized_similarity=0.5')

            # 反向线性映射到K值: K = K_max - (K_max - K_min) * normalized_similarity
            k_float = self.k_max - (self.k_max - self.k_min) * normalized_similarity
            k_final = round(k_float)

            # 确保K值在合理范围内
            k_final = max(self.k_min, min(self.k_max, min(k_final, init_cls)))
            k_values.append(k_final)

            logging.debug(f'[Dynamic-K] Class {i}: avg_sim={avg_similarity:.4f}, '
                         f'normalized={normalized_similarity:.4f}, '
                         f'k_float={k_float:.2f}, K_final={k_final}')

        logging.info(f'[Dynamic-K] Computed K values: {k_values}')
        return k_values


    def incremental_train(self, data_manager):
        self._cur_task += 1
        self._total_classes = self._known_classes + data_manager.get_task_size(self._cur_task)
        self._network.update_fc(self._total_classes)
        logging.info("Learning on {}-{}".format(self._known_classes, self._total_classes))

        if self._cur_task > 0:
            self.shot = self.args["shot"]
        else:
            self.shot = None

        train_dataset = data_manager.get_dataset(np.arange(self._known_classes, self._total_classes),source="train", mode="train", shot=self.shot, )

        self.train_dataset=train_dataset
        self.data_manager=data_manager
        self.train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True, num_workers=num_workers)

        test_dataset = data_manager.get_dataset(np.arange(0, self._total_classes), source="test", mode="test" )
        self.test_loader = DataLoader(test_dataset, batch_size=self.batch_size, shuffle=False, num_workers=num_workers)

        train_dataset_for_protonet=data_manager.get_dataset(np.arange(self._known_classes, self._total_classes),source="train", mode="test", shot=self.shot, )
        self.train_loader_for_protonet = DataLoader(train_dataset_for_protonet, batch_size=self.batch_size, shuffle=True, num_workers=num_workers)

        if len(self._multiple_gpus) > 1:
            print('Multiple GPUs')
            self._network = nn.DataParallel(self._network, self._multiple_gpus)
        self._train(self.train_loader, self.test_loader, self.train_loader_for_protonet)
        if len(self._multiple_gpus) > 1:
            self._network = self._network.module

    def _train(self, train_loader, test_loader, train_loader_for_protonet):
        
        self._network.to(self._device)
        
        if self._cur_task == 0:
            # show total parameters and trainable parameters
            total_params = sum(p.numel() for p in self._network.parameters())
            print(f'{total_params:,} total parameters.')
            total_trainable_params = sum(
                p.numel() for p in self._network.parameters() if p.requires_grad)
            print(f'{total_trainable_params:,} training parameters.')
            if total_params != total_trainable_params:
                for name, param in self._network.named_parameters():
                    if param.requires_grad:
                        print(name, param.numel())
            if self.args['optimizer']=='sgd':
                optimizer = optim.SGD(self._network.parameters(), momentum=0.9, lr=self.init_lr,weight_decay=self.weight_decay)
            elif self.args['optimizer']=='adam':
                optimizer=optim.AdamW(self._network.parameters(), lr=self.init_lr, weight_decay=self.weight_decay)
            scheduler=optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=self.args['tuned_epoch'], eta_min=self.min_lr)
            if not self.args['resume']:
                self._init_train(train_loader, test_loader, optimizer, scheduler)
                self.save_checkpoint("weights/{}_{}_{}_{}_{}".format(self.args["dataset"],self.args["model_name"],self.args["seed"],self.args["init_cls"],self.args["increment"]))
                self._network.to(self._device)
            else:
                self._network.load_state_dict(torch.load("weights/{}_{}_{}_{}_{}_{}.pkl".format(self.args["dataset"],self.args["model_name"],self.args["seed"],self.args["init_cls"],self.args["increment"],self._cur_task))["model_state_dict"])
                self._network.to(self._device)
            # self.construct_dual_branch_network()
        else:
            pass
        self.replace_fc(train_loader_for_protonet, self._network, self.args)
            

    def construct_dual_branch_network(self):
        network = MultiBranchCosineIncrementalNet(self.args, True)
        network.construct_dual_branch_network(self._network)
        self._network=network.to(self._device)

    def _init_train(self, train_loader, test_loader, optimizer, scheduler):
        prog_bar = tqdm(range(self.args['tuned_epoch']))
        for _, epoch in enumerate(prog_bar):
            self._network.train()
            losses = 0.0
            correct, total = 0, 0
            for i, (_, inputs, targets) in enumerate(train_loader):
                inputs, targets = inputs.to(self._device), targets.to(self._device)
                logits = self._network(inputs)["logits"]

                loss = F.cross_entropy(logits, targets)
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                losses += loss.item()

                _, preds = torch.max(logits, dim=1)
                correct += preds.eq(targets.expand_as(preds)).cpu().sum()
                total += len(targets)

            scheduler.step()
            train_acc = np.around(tensor2numpy(correct) * 100 / total, decimals=2)

            test_acc = self._compute_accuracy(self._network, test_loader)
            info = "Task {}, Epoch {}/{} => Loss {:.3f}, Train_accy {:.2f}, Test_accy {:.2f}".format(
                self._cur_task,
                epoch + 1,
                self.args['tuned_epoch'],
                losses / len(train_loader),
                train_acc,
                test_acc,
            )
            prog_bar.set_description(info)

        logging.info(info)
