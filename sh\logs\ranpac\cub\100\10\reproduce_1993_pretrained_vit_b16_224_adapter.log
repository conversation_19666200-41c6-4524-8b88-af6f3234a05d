2025-07-14 18:06:53,384 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/ranpac.json
2025-07-14 18:06:53,387 [trainer.py] => prefix: reproduce
2025-07-14 18:06:53,387 [trainer.py] => dataset: cub
2025-07-14 18:06:53,387 [trainer.py] => memory_size: 0
2025-07-14 18:06:53,387 [trainer.py] => shuffle: True
2025-07-14 18:06:53,387 [trainer.py] => init_cls: 100
2025-07-14 18:06:53,388 [trainer.py] => increment: 10
2025-07-14 18:06:53,388 [trainer.py] => model_name: ranpac
2025-07-14 18:06:53,388 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 18:06:53,388 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 18:06:53,388 [trainer.py] => seed: 1993
2025-07-14 18:06:53,388 [trainer.py] => resume: False
2025-07-14 18:06:53,388 [trainer.py] => shot: 5
2025-07-14 18:06:53,388 [trainer.py] => use_simplecil: False
2025-07-14 18:06:53,388 [trainer.py] => tuned_epoch: 40
2025-07-14 18:06:53,389 [trainer.py] => init_lr: 0.01
2025-07-14 18:06:53,389 [trainer.py] => batch_size: 48
2025-07-14 18:06:53,389 [trainer.py] => weight_decay: 0.0005
2025-07-14 18:06:53,389 [trainer.py] => min_lr: 0
2025-07-14 18:06:53,389 [trainer.py] => ffn_num: 64
2025-07-14 18:06:53,389 [trainer.py] => optimizer: sgd
2025-07-14 18:06:53,389 [trainer.py] => use_RP: True
2025-07-14 18:06:53,389 [trainer.py] => M: 10000
2025-07-14 18:06:53,389 [trainer.py] => fecam: False
2025-07-14 18:06:53,389 [trainer.py] => calibration: True
2025-07-14 18:06:53,389 [trainer.py] => knn_k: 5
2025-07-14 18:06:53,390 [trainer.py] => knn_distance_metric: cosine
2025-07-14 18:06:53,390 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 18:06:53,390 [trainer.py] => knn_adaptive_k: False
2025-07-14 18:06:53,390 [trainer.py] => knn_temperature: 16.0
2025-07-14 18:06:53,630 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 18:07:30,257 [trainer.py] => All params: 86988288
2025-07-14 18:07:30,257 [trainer.py] => Trainable params: 1189632
2025-07-14 18:07:46,761 [ranpac.py] => Learning on 0-100
2025-07-14 18:28:10,436 [ranpac.py] => Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48
2025-07-14 18:35:29,873 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/ranpac.json
2025-07-14 18:35:29,878 [trainer.py] => prefix: reproduce
2025-07-14 18:35:29,878 [trainer.py] => dataset: cub
2025-07-14 18:35:29,878 [trainer.py] => memory_size: 0
2025-07-14 18:35:29,878 [trainer.py] => shuffle: True
2025-07-14 18:35:29,878 [trainer.py] => init_cls: 100
2025-07-14 18:35:29,878 [trainer.py] => increment: 10
2025-07-14 18:35:29,879 [trainer.py] => model_name: ranpac
2025-07-14 18:35:29,879 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 18:35:29,879 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 18:35:29,879 [trainer.py] => seed: 1993
2025-07-14 18:35:29,879 [trainer.py] => resume: False
2025-07-14 18:35:29,879 [trainer.py] => shot: 5
2025-07-14 18:35:29,879 [trainer.py] => use_simplecil: False
2025-07-14 18:35:29,879 [trainer.py] => tuned_epoch: 1
2025-07-14 18:35:29,880 [trainer.py] => init_lr: 0.01
2025-07-14 18:35:29,880 [trainer.py] => batch_size: 48
2025-07-14 18:35:29,880 [trainer.py] => weight_decay: 0.0005
2025-07-14 18:35:29,880 [trainer.py] => min_lr: 0
2025-07-14 18:35:29,880 [trainer.py] => ffn_num: 64
2025-07-14 18:35:29,880 [trainer.py] => optimizer: sgd
2025-07-14 18:35:29,880 [trainer.py] => use_RP: True
2025-07-14 18:35:29,881 [trainer.py] => M: 10000
2025-07-14 18:35:29,881 [trainer.py] => fecam: False
2025-07-14 18:35:29,881 [trainer.py] => calibration: True
2025-07-14 18:35:29,881 [trainer.py] => knn_k: 5
2025-07-14 18:35:29,881 [trainer.py] => knn_distance_metric: cosine
2025-07-14 18:35:29,881 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 18:35:29,881 [trainer.py] => knn_adaptive_k: False
2025-07-14 18:35:29,881 [trainer.py] => knn_temperature: 16.0
2025-07-14 18:35:30,048 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 18:36:00,142 [trainer.py] => All params: 86988288
2025-07-14 18:36:00,143 [trainer.py] => Trainable params: 1189632
2025-07-14 18:36:01,384 [ranpac.py] => Learning on 0-100
2025-07-14 18:36:40,493 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-14 18:37:30,378 [trainer.py] => No NME accuracy.
2025-07-14 18:37:30,381 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-14 18:37:30,381 [trainer.py] => CNN HM: [0.0]
2025-07-14 18:37:30,381 [trainer.py] => CNN top1 curve: [90.43]
2025-07-14 18:37:30,381 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-14 18:37:30,383 [trainer.py] => All params: 87988289
2025-07-14 18:37:30,384 [trainer.py] => Trainable params: 1189633
2025-07-14 18:37:30,398 [ranpac.py] => Learning on 100-110
2025-07-14 18:37:33,361 [ranpac.py] => [KNN] task 1, fixed K=5
2025-07-14 18:37:35,373 [ranpac.py] => [KNN] task 1, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:38:00,079 [trainer.py] => No NME accuracy.
2025-07-14 18:38:00,082 [trainer.py] => CNN: {'total': 89.63, '00-99': 89.88, '100-109': 87.16, 'old': 89.88, 'new': 87.16}
2025-07-14 18:38:00,082 [trainer.py] => CNN HM: [0.0, 88.499]
2025-07-14 18:38:00,083 [trainer.py] => CNN top1 curve: [90.43, 89.63]
2025-07-14 18:38:00,083 [trainer.py] => Average Accuracy (CNN): 90.03 

2025-07-14 18:38:00,084 [trainer.py] => All params: 88088289
2025-07-14 18:38:00,085 [trainer.py] => Trainable params: 2289633
2025-07-14 18:38:00,098 [ranpac.py] => Learning on 110-120
2025-07-14 18:38:04,071 [ranpac.py] => [KNN] task 2, fixed K=5
2025-07-14 18:38:04,074 [ranpac.py] => [KNN] task 2, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:38:27,693 [trainer.py] => No NME accuracy.
2025-07-14 18:38:27,695 [trainer.py] => CNN: {'total': 87.76, '00-99': 89.29, '100-109': 88.18, '110-119': 71.83, 'old': 89.19, 'new': 71.83}
2025-07-14 18:38:27,696 [trainer.py] => CNN HM: [0.0, 88.499, 79.574]
2025-07-14 18:38:27,696 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76]
2025-07-14 18:38:27,697 [trainer.py] => Average Accuracy (CNN): 89.27333333333333 

2025-07-14 18:38:27,698 [trainer.py] => All params: 88188289
2025-07-14 18:38:27,698 [trainer.py] => Trainable params: 2389633
2025-07-14 18:38:27,720 [ranpac.py] => Learning on 120-130
2025-07-14 18:38:31,258 [ranpac.py] => [KNN] task 3, fixed K=5
2025-07-14 18:38:31,261 [ranpac.py] => [KNN] task 3, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:38:56,463 [trainer.py] => No NME accuracy.
2025-07-14 18:38:56,465 [trainer.py] => CNN: {'total': 86.39, '00-99': 88.8, '100-109': 89.53, '110-119': 74.65, '120-129': 70.69, 'old': 87.71, 'new': 70.69}
2025-07-14 18:38:56,465 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 78.286]
2025-07-14 18:38:56,465 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.39]
2025-07-14 18:38:56,465 [trainer.py] => Average Accuracy (CNN): 88.5525 

2025-07-14 18:38:56,466 [trainer.py] => All params: 88288289
2025-07-14 18:38:56,467 [trainer.py] => Trainable params: 2489633
2025-07-14 18:38:56,483 [ranpac.py] => Learning on 130-140
2025-07-14 18:39:00,072 [ranpac.py] => [KNN] task 4, fixed K=5
2025-07-14 18:39:00,075 [ranpac.py] => [KNN] task 4, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:39:29,805 [trainer.py] => No NME accuracy.
2025-07-14 18:39:29,807 [trainer.py] => CNN: {'total': 85.71, '00-99': 88.46, '100-109': 89.86, '110-119': 74.3, '120-129': 68.62, '130-139': 82.41, 'old': 85.97, 'new': 82.41}
2025-07-14 18:39:29,807 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 78.286, 84.152]
2025-07-14 18:39:29,808 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.39, 85.71]
2025-07-14 18:39:29,808 [trainer.py] => Average Accuracy (CNN): 87.984 

2025-07-14 18:39:29,810 [trainer.py] => All params: 88388289
2025-07-14 18:39:29,811 [trainer.py] => Trainable params: 2589633
2025-07-14 18:39:29,825 [ranpac.py] => Learning on 140-150
2025-07-14 18:39:33,376 [ranpac.py] => [KNN] task 5, fixed K=5
2025-07-14 18:39:33,377 [ranpac.py] => [KNN] task 5, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:40:04,047 [trainer.py] => No NME accuracy.
2025-07-14 18:40:04,049 [trainer.py] => CNN: {'total': 84.32, '00-99': 87.9, '100-109': 90.54, '110-119': 72.18, '120-129': 68.62, '130-139': 83.45, '140-149': 70.14, 'old': 85.29, 'new': 70.14}
2025-07-14 18:40:04,049 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 78.286, 84.152, 76.977]
2025-07-14 18:40:04,049 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.39, 85.71, 84.32]
2025-07-14 18:40:04,049 [trainer.py] => Average Accuracy (CNN): 87.37333333333333 

2025-07-14 18:40:04,051 [trainer.py] => All params: 88488289
2025-07-14 18:40:04,052 [trainer.py] => Trainable params: 2689633
2025-07-14 18:40:04,068 [ranpac.py] => Learning on 150-160
2025-07-14 18:40:08,582 [ranpac.py] => [KNN] task 6, fixed K=5
2025-07-14 18:40:08,583 [ranpac.py] => [KNN] task 6, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:40:40,432 [trainer.py] => No NME accuracy.
2025-07-14 18:40:40,434 [trainer.py] => CNN: {'total': 83.88, '00-99': 87.42, '100-109': 88.18, '110-119': 73.59, '120-129': 69.66, '130-139': 83.79, '140-149': 70.5, '150-159': 81.51, 'old': 84.04, 'new': 81.51}
2025-07-14 18:40:40,435 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 78.286, 84.152, 76.977, 82.756]
2025-07-14 18:40:40,435 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.39, 85.71, 84.32, 83.88]
2025-07-14 18:40:40,435 [trainer.py] => Average Accuracy (CNN): 86.87428571428572 

2025-07-14 18:40:40,437 [trainer.py] => All params: 88588289
2025-07-14 18:40:40,439 [trainer.py] => Trainable params: 2789633
2025-07-14 18:40:40,455 [ranpac.py] => Learning on 160-170
2025-07-14 18:40:43,152 [ranpac.py] => [KNN] task 7, fixed K=5
2025-07-14 18:40:43,154 [ranpac.py] => [KNN] task 7, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:41:15,640 [trainer.py] => No NME accuracy.
2025-07-14 18:41:15,643 [trainer.py] => CNN: {'total': 83.19, '00-99': 87.14, '100-109': 88.51, '110-119': 73.94, '120-129': 70.34, '130-139': 82.41, '140-149': 66.91, '150-159': 80.48, '160-169': 79.53, 'old': 83.42, 'new': 79.53}
2025-07-14 18:41:15,643 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 78.286, 84.152, 76.977, 82.756, 81.429]
2025-07-14 18:41:15,644 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.39, 85.71, 84.32, 83.88, 83.19]
2025-07-14 18:41:15,644 [trainer.py] => Average Accuracy (CNN): 86.41375 

2025-07-14 18:41:15,646 [trainer.py] => All params: 88688289
2025-07-14 18:41:15,649 [trainer.py] => Trainable params: 2889633
2025-07-14 18:41:15,667 [ranpac.py] => Learning on 170-180
2025-07-14 18:41:18,138 [ranpac.py] => [KNN] task 8, fixed K=5
2025-07-14 18:41:18,138 [ranpac.py] => [KNN] task 8, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:41:53,583 [trainer.py] => No NME accuracy.
2025-07-14 18:41:53,585 [trainer.py] => CNN: {'total': 82.33, '00-99': 86.2, '100-109': 88.18, '110-119': 73.94, '120-129': 70.34, '130-139': 81.72, '140-149': 65.11, '150-159': 80.82, '160-169': 79.19, '170-179': 79.87, 'old': 82.48, 'new': 79.87}
2025-07-14 18:41:53,585 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 78.286, 84.152, 76.977, 82.756, 81.429, 81.154]
2025-07-14 18:41:53,585 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.39, 85.71, 84.32, 83.88, 83.19, 82.33]
2025-07-14 18:41:53,586 [trainer.py] => Average Accuracy (CNN): 85.96 

2025-07-14 18:41:53,587 [trainer.py] => All params: 88788289
2025-07-14 18:41:53,588 [trainer.py] => Trainable params: 2989633
2025-07-14 18:41:53,604 [ranpac.py] => Learning on 180-190
2025-07-14 18:41:56,974 [ranpac.py] => [KNN] task 9, fixed K=5
2025-07-14 18:41:56,977 [ranpac.py] => [KNN] task 9, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:42:25,802 [trainer.py] => No NME accuracy.
2025-07-14 18:42:25,804 [trainer.py] => CNN: {'total': 81.72, '00-99': 85.89, '100-109': 88.51, '110-119': 74.3, '120-129': 69.31, '130-139': 80.0, '140-149': 64.39, '150-159': 80.82, '160-169': 79.19, '170-179': 79.53, '180-189': 77.0, 'old': 81.98, 'new': 77.0}
2025-07-14 18:42:25,804 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 78.286, 84.152, 76.977, 82.756, 81.429, 81.154, 79.412]
2025-07-14 18:42:25,804 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.39, 85.71, 84.32, 83.88, 83.19, 82.33, 81.72]
2025-07-14 18:42:25,804 [trainer.py] => Average Accuracy (CNN): 85.536 

2025-07-14 18:42:25,805 [trainer.py] => All params: 88888289
2025-07-14 18:42:25,806 [trainer.py] => Trainable params: 3089633
2025-07-14 18:42:25,824 [ranpac.py] => Learning on 190-200
2025-07-14 18:42:28,719 [ranpac.py] => [KNN] task 10, fixed K=5
2025-07-14 18:42:28,720 [ranpac.py] => [KNN] task 10, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 18:42:56,221 [trainer.py] => No NME accuracy.
2025-07-14 18:42:56,226 [trainer.py] => CNN: {'total': 80.81, '00-99': 84.89, '100-109': 89.19, '110-119': 73.59, '120-129': 68.62, '130-139': 81.03, '140-149': 65.11, '150-159': 80.82, '160-169': 79.19, '170-179': 79.19, '180-189': 75.96, '190-199': 73.99, 'old': 81.17, 'new': 73.99}
2025-07-14 18:42:56,226 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 78.286, 84.152, 76.977, 82.756, 81.429, 81.154, 79.412, 77.414]
2025-07-14 18:42:56,226 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.39, 85.71, 84.32, 83.88, 83.19, 82.33, 81.72, 80.81]
2025-07-14 18:42:56,226 [trainer.py] => Average Accuracy (CNN): 85.10636363636364 

2025-07-14 18:42:56,227 [trainer.py] => Forgetting (CNN): 2.0920000000000045
2025-07-14 18:43:39,429 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/ranpac.json
2025-07-14 18:43:39,430 [trainer.py] => prefix: reproduce
2025-07-14 18:43:39,430 [trainer.py] => dataset: cub
2025-07-14 18:43:39,430 [trainer.py] => memory_size: 0
2025-07-14 18:43:39,431 [trainer.py] => shuffle: True
2025-07-14 18:43:39,431 [trainer.py] => init_cls: 100
2025-07-14 18:43:39,431 [trainer.py] => increment: 10
2025-07-14 18:43:39,431 [trainer.py] => model_name: ranpac
2025-07-14 18:43:39,431 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 18:43:39,431 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 18:43:39,431 [trainer.py] => seed: 1993
2025-07-14 18:43:39,432 [trainer.py] => resume: False
2025-07-14 18:43:39,432 [trainer.py] => shot: 5
2025-07-14 18:43:39,432 [trainer.py] => use_simplecil: False
2025-07-14 18:43:39,432 [trainer.py] => tuned_epoch: 40
2025-07-14 18:43:39,432 [trainer.py] => init_lr: 0.01
2025-07-14 18:43:39,432 [trainer.py] => batch_size: 48
2025-07-14 18:43:39,432 [trainer.py] => weight_decay: 0.0005
2025-07-14 18:43:39,432 [trainer.py] => min_lr: 0
2025-07-14 18:43:39,433 [trainer.py] => ffn_num: 64
2025-07-14 18:43:39,433 [trainer.py] => optimizer: sgd
2025-07-14 18:43:39,433 [trainer.py] => use_RP: True
2025-07-14 18:43:39,433 [trainer.py] => M: 10000
2025-07-14 18:43:39,433 [trainer.py] => fecam: False
2025-07-14 18:43:39,433 [trainer.py] => calibration: True
2025-07-14 18:43:39,433 [trainer.py] => knn_k: 5
2025-07-14 18:43:39,433 [trainer.py] => knn_distance_metric: cosine
2025-07-14 18:43:39,433 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 18:43:39,434 [trainer.py] => knn_adaptive_k: False
2025-07-14 18:43:39,434 [trainer.py] => knn_temperature: 16.0
2025-07-14 18:43:39,582 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 18:44:03,610 [trainer.py] => All params: 86988288
2025-07-14 18:44:03,610 [trainer.py] => Trainable params: 1189632
2025-07-14 18:44:04,847 [ranpac.py] => Learning on 0-100
2025-07-14 19:10:17,963 [ranpac.py] => Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48
2025-07-14 19:11:04,872 [trainer.py] => No NME accuracy.
2025-07-14 19:11:04,874 [trainer.py] => CNN: {'total': 92.44, '00-99': 92.44, 'old': 0, 'new': 92.44}
2025-07-14 19:11:04,874 [trainer.py] => CNN HM: [0.0]
2025-07-14 19:11:04,875 [trainer.py] => CNN top1 curve: [92.44]
2025-07-14 19:11:04,875 [trainer.py] => Average Accuracy (CNN): 92.44 

2025-07-14 19:11:04,877 [trainer.py] => All params: 87988289
2025-07-14 19:11:04,878 [trainer.py] => Trainable params: 1189633
2025-07-14 19:11:04,890 [ranpac.py] => Learning on 100-110
2025-07-14 19:11:09,029 [ranpac.py] => [KNN] task 1, fixed K=5
2025-07-14 19:11:09,033 [ranpac.py] => [KNN] task 1, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:11:32,655 [trainer.py] => No NME accuracy.
2025-07-14 19:11:32,657 [trainer.py] => CNN: {'total': 91.48, '00-99': 91.72, '100-109': 89.19, 'old': 91.72, 'new': 89.19}
2025-07-14 19:11:32,657 [trainer.py] => CNN HM: [0.0, 90.437]
2025-07-14 19:11:32,658 [trainer.py] => CNN top1 curve: [92.44, 91.48]
2025-07-14 19:11:32,658 [trainer.py] => Average Accuracy (CNN): 91.96000000000001 

2025-07-14 19:11:32,658 [trainer.py] => All params: 88088289
2025-07-14 19:11:32,659 [trainer.py] => Trainable params: 2289633
2025-07-14 19:11:32,669 [ranpac.py] => Learning on 110-120
2025-07-14 19:11:36,246 [ranpac.py] => [KNN] task 2, fixed K=5
2025-07-14 19:11:36,250 [ranpac.py] => [KNN] task 2, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:11:58,720 [trainer.py] => No NME accuracy.
2025-07-14 19:11:58,723 [trainer.py] => CNN: {'total': 90.3, '00-99': 91.61, '100-109': 89.86, '110-119': 77.46, 'old': 91.45, 'new': 77.46}
2025-07-14 19:11:58,723 [trainer.py] => CNN HM: [0.0, 90.437, 83.876]
2025-07-14 19:11:58,723 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3]
2025-07-14 19:11:58,723 [trainer.py] => Average Accuracy (CNN): 91.40666666666668 

2025-07-14 19:11:58,725 [trainer.py] => All params: 88188289
2025-07-14 19:11:58,727 [trainer.py] => Trainable params: 2389633
2025-07-14 19:11:58,740 [ranpac.py] => Learning on 120-130
2025-07-14 19:12:02,265 [ranpac.py] => [KNN] task 3, fixed K=5
2025-07-14 19:12:02,266 [ranpac.py] => [KNN] task 3, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:12:27,210 [trainer.py] => No NME accuracy.
2025-07-14 19:12:27,212 [trainer.py] => CNN: {'total': 89.19, '00-99': 91.23, '100-109': 91.55, '110-119': 78.17, '120-129': 77.24, 'old': 90.19, 'new': 77.24}
2025-07-14 19:12:27,212 [trainer.py] => CNN HM: [0.0, 90.437, 83.876, 83.214]
2025-07-14 19:12:27,212 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3, 89.19]
2025-07-14 19:12:27,212 [trainer.py] => Average Accuracy (CNN): 90.8525 

2025-07-14 19:12:27,213 [trainer.py] => All params: 88288289
2025-07-14 19:12:27,213 [trainer.py] => Trainable params: 2489633
2025-07-14 19:12:27,230 [ranpac.py] => Learning on 130-140
2025-07-14 19:12:31,194 [ranpac.py] => [KNN] task 4, fixed K=5
2025-07-14 19:12:31,196 [ranpac.py] => [KNN] task 4, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:12:54,772 [trainer.py] => No NME accuracy.
2025-07-14 19:12:54,775 [trainer.py] => CNN: {'total': 88.48, '00-99': 90.68, '100-109': 92.57, '110-119': 78.52, '120-129': 74.14, '130-139': 86.55, 'old': 88.63, 'new': 86.55}
2025-07-14 19:12:54,775 [trainer.py] => CNN HM: [0.0, 90.437, 83.876, 83.214, 87.578]
2025-07-14 19:12:54,775 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3, 89.19, 88.48]
2025-07-14 19:12:54,775 [trainer.py] => Average Accuracy (CNN): 90.37800000000001 

2025-07-14 19:12:54,777 [trainer.py] => All params: 88388289
2025-07-14 19:12:54,777 [trainer.py] => Trainable params: 2589633
2025-07-14 19:12:54,791 [ranpac.py] => Learning on 140-150
2025-07-14 19:12:58,072 [ranpac.py] => [KNN] task 5, fixed K=5
2025-07-14 19:12:58,075 [ranpac.py] => [KNN] task 5, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:13:28,332 [trainer.py] => No NME accuracy.
2025-07-14 19:13:28,334 [trainer.py] => CNN: {'total': 87.37, '00-99': 90.02, '100-109': 92.91, '110-119': 78.52, '120-129': 75.52, '130-139': 87.24, '140-149': 75.54, 'old': 88.18, 'new': 75.54}
2025-07-14 19:13:28,334 [trainer.py] => CNN HM: [0.0, 90.437, 83.876, 83.214, 87.578, 81.372]
2025-07-14 19:13:28,334 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3, 89.19, 88.48, 87.37]
2025-07-14 19:13:28,334 [trainer.py] => Average Accuracy (CNN): 89.87666666666667 

2025-07-14 19:13:28,335 [trainer.py] => All params: 88488289
2025-07-14 19:13:28,336 [trainer.py] => Trainable params: 2689633
2025-07-14 19:13:28,353 [ranpac.py] => Learning on 150-160
2025-07-14 19:13:32,158 [ranpac.py] => [KNN] task 6, fixed K=5
2025-07-14 19:13:32,160 [ranpac.py] => [KNN] task 6, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:14:05,311 [trainer.py] => No NME accuracy.
2025-07-14 19:14:05,313 [trainer.py] => CNN: {'total': 87.09, '00-99': 89.98, '100-109': 90.54, '110-119': 79.23, '120-129': 76.55, '130-139': 87.24, '140-149': 75.18, '150-159': 84.25, 'old': 87.28, 'new': 84.25}
2025-07-14 19:14:05,314 [trainer.py] => CNN HM: [0.0, 90.437, 83.876, 83.214, 87.578, 81.372, 85.738]
2025-07-14 19:14:05,314 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3, 89.19, 88.48, 87.37, 87.09]
2025-07-14 19:14:05,314 [trainer.py] => Average Accuracy (CNN): 89.47857142857143 

2025-07-14 19:14:05,315 [trainer.py] => All params: 88588289
2025-07-14 19:14:05,315 [trainer.py] => Trainable params: 2789633
2025-07-14 19:14:05,332 [ranpac.py] => Learning on 160-170
2025-07-14 19:14:09,414 [ranpac.py] => [KNN] task 7, fixed K=5
2025-07-14 19:14:09,417 [ranpac.py] => [KNN] task 7, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:14:37,014 [trainer.py] => No NME accuracy.
2025-07-14 19:14:37,016 [trainer.py] => CNN: {'total': 86.61, '00-99': 89.84, '100-109': 90.88, '110-119': 78.52, '120-129': 76.55, '130-139': 86.55, '140-149': 70.5, '150-159': 85.27, '160-169': 84.9, 'old': 86.72, 'new': 84.9}
2025-07-14 19:14:37,017 [trainer.py] => CNN HM: [0.0, 90.437, 83.876, 83.214, 87.578, 81.372, 85.738, 85.8]
2025-07-14 19:14:37,017 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3, 89.19, 88.48, 87.37, 87.09, 86.61]
2025-07-14 19:14:37,017 [trainer.py] => Average Accuracy (CNN): 89.12 

2025-07-14 19:14:37,017 [trainer.py] => All params: 88688289
2025-07-14 19:14:37,018 [trainer.py] => Trainable params: 2889633
2025-07-14 19:14:37,033 [ranpac.py] => Learning on 170-180
2025-07-14 19:14:39,697 [ranpac.py] => [KNN] task 8, fixed K=5
2025-07-14 19:14:39,700 [ranpac.py] => [KNN] task 8, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:15:08,698 [trainer.py] => No NME accuracy.
2025-07-14 19:15:08,700 [trainer.py] => CNN: {'total': 86.34, '00-99': 89.5, '100-109': 90.88, '110-119': 78.17, '120-129': 75.52, '130-139': 86.9, '140-149': 70.86, '150-159': 84.59, '160-169': 85.57, '170-179': 85.91, 'old': 86.36, 'new': 85.91}
2025-07-14 19:15:08,700 [trainer.py] => CNN HM: [0.0, 90.437, 83.876, 83.214, 87.578, 81.372, 85.738, 85.8, 86.134]
2025-07-14 19:15:08,700 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3, 89.19, 88.48, 87.37, 87.09, 86.61, 86.34]
2025-07-14 19:15:08,700 [trainer.py] => Average Accuracy (CNN): 88.81111111111112 

2025-07-14 19:15:08,701 [trainer.py] => All params: 88788289
2025-07-14 19:15:08,702 [trainer.py] => Trainable params: 2989633
2025-07-14 19:15:08,720 [ranpac.py] => Learning on 180-190
2025-07-14 19:15:13,601 [ranpac.py] => [KNN] task 9, fixed K=5
2025-07-14 19:15:13,604 [ranpac.py] => [KNN] task 9, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:15:44,036 [trainer.py] => No NME accuracy.
2025-07-14 19:15:44,038 [trainer.py] => CNN: {'total': 85.43, '00-99': 89.01, '100-109': 90.2, '110-119': 77.82, '120-129': 75.17, '130-139': 84.48, '140-149': 71.22, '150-159': 84.25, '160-169': 85.23, '170-179': 84.9, '180-189': 79.09, 'old': 85.78, 'new': 79.09}
2025-07-14 19:15:44,038 [trainer.py] => CNN HM: [0.0, 90.437, 83.876, 83.214, 87.578, 81.372, 85.738, 85.8, 86.134, 82.299]
2025-07-14 19:15:44,038 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3, 89.19, 88.48, 87.37, 87.09, 86.61, 86.34, 85.43]
2025-07-14 19:15:44,039 [trainer.py] => Average Accuracy (CNN): 88.473 

2025-07-14 19:15:44,040 [trainer.py] => All params: 88888289
2025-07-14 19:15:44,041 [trainer.py] => Trainable params: 3089633
2025-07-14 19:15:44,057 [ranpac.py] => Learning on 190-200
2025-07-14 19:15:47,120 [ranpac.py] => [KNN] task 10, fixed K=5
2025-07-14 19:15:47,122 [ranpac.py] => [KNN] task 10, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 19:16:21,503 [trainer.py] => No NME accuracy.
2025-07-14 19:16:21,506 [trainer.py] => CNN: {'total': 84.76, '00-99': 88.18, '100-109': 90.54, '110-119': 78.52, '120-129': 75.17, '130-139': 85.17, '140-149': 71.94, '150-159': 85.62, '160-169': 83.89, '170-179': 84.56, '180-189': 79.79, '190-199': 77.7, 'old': 85.14, 'new': 77.7}
2025-07-14 19:16:21,506 [trainer.py] => CNN HM: [0.0, 90.437, 83.876, 83.214, 87.578, 81.372, 85.738, 85.8, 86.134, 82.299, 81.25]
2025-07-14 19:16:21,506 [trainer.py] => CNN top1 curve: [92.44, 91.48, 90.3, 89.19, 88.48, 87.37, 87.09, 86.61, 86.34, 85.43, 84.76]
2025-07-14 19:16:21,506 [trainer.py] => Average Accuracy (CNN): 88.13545454545455 

2025-07-14 19:16:21,508 [trainer.py] => Forgetting (CNN): 1.810999999999997
2025-07-14 21:17:03,231 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/ranpac.json
2025-07-14 21:17:03,234 [trainer.py] => prefix: reproduce
2025-07-14 21:17:03,234 [trainer.py] => dataset: cub
2025-07-14 21:17:03,235 [trainer.py] => memory_size: 0
2025-07-14 21:17:03,235 [trainer.py] => shuffle: True
2025-07-14 21:17:03,236 [trainer.py] => init_cls: 100
2025-07-14 21:17:03,236 [trainer.py] => increment: 10
2025-07-14 21:17:03,237 [trainer.py] => model_name: ranpac
2025-07-14 21:17:03,237 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 21:17:03,238 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 21:17:03,238 [trainer.py] => seed: 1993
2025-07-14 21:17:03,239 [trainer.py] => resume: False
2025-07-14 21:17:03,239 [trainer.py] => shot: 5
2025-07-14 21:17:03,239 [trainer.py] => use_simplecil: False
2025-07-14 21:17:03,240 [trainer.py] => tuned_epoch: 40
2025-07-14 21:17:03,240 [trainer.py] => init_lr: 0.01
2025-07-14 21:17:03,241 [trainer.py] => batch_size: 48
2025-07-14 21:17:03,242 [trainer.py] => weight_decay: 0.0005
2025-07-14 21:17:03,242 [trainer.py] => min_lr: 0
2025-07-14 21:17:03,242 [trainer.py] => ffn_num: 64
2025-07-14 21:17:03,242 [trainer.py] => optimizer: sgd
2025-07-14 21:17:03,242 [trainer.py] => use_RP: True
2025-07-14 21:17:03,242 [trainer.py] => M: 10000
2025-07-14 21:17:03,242 [trainer.py] => fecam: False
2025-07-14 21:17:03,242 [trainer.py] => calibration: True
2025-07-14 21:17:03,243 [trainer.py] => knn_k: 5
2025-07-14 21:17:03,243 [trainer.py] => knn_distance_metric: cosine
2025-07-14 21:17:03,243 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 21:17:03,243 [trainer.py] => knn_adaptive_k: True
2025-07-14 21:17:03,243 [trainer.py] => knn_temperature: 16.0
2025-07-14 21:17:03,243 [trainer.py] => k_min: 3
2025-07-14 21:17:03,243 [trainer.py] => k_max: 21
2025-07-14 21:17:03,243 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-14 21:17:03,244 [trainer.py] => cosine_temperature: 16.0
2025-07-14 21:17:03,360 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 21:17:51,563 [trainer.py] => All params: 86988288
2025-07-14 21:17:51,566 [trainer.py] => Trainable params: 1189632
2025-07-14 21:18:12,816 [ranpac.py] => Learning on 0-100
2025-07-14 21:41:39,283 [ranpac.py] => Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48
2025-07-14 21:42:20,756 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-14 21:42:21,203 [ranpac.py] => [Dynamic-K] Base similarity range: [-0.0264, 0.8900]
2025-07-14 21:42:21,204 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-14 21:42:21,204 [trainer.py] => No NME accuracy.
2025-07-14 21:42:21,204 [trainer.py] => CNN: {'total': 92.44, '00-99': 92.44, 'old': 0, 'new': 92.44}
2025-07-14 21:42:21,204 [trainer.py] => CNN HM: [0.0]
2025-07-14 21:42:21,204 [trainer.py] => CNN top1 curve: [92.44]
2025-07-14 21:42:21,204 [trainer.py] => Average Accuracy (CNN): 92.44 

2025-07-14 21:42:21,204 [trainer.py] => All params: 87988289
2025-07-14 21:42:21,205 [trainer.py] => Trainable params: 1189633
2025-07-14 21:42:21,440 [ranpac.py] => Learning on 100-110
2025-07-14 21:42:24,718 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:42:24,741 [ranpac.py] => [Dynamic-K] Computed K values: [13, 14, 7, 14, 7, 7, 13, 10, 12, 8]
2025-07-14 21:42:24,742 [ranpac.py] => [KNN] task 1, dynamic K values: [13, 14, 7, 14, 7, 7, 13, 10, 12, 8]
2025-07-14 21:42:25,158 [ranpac.py] => [KNN] task 1, weight sparsity: 0.895, distance_metric: cosine
2025-07-14 21:42:45,922 [trainer.py] => No NME accuracy.
2025-07-14 21:42:45,924 [trainer.py] => CNN: {'total': 91.54, '00-99': 91.79, '100-109': 89.19, 'old': 91.79, 'new': 89.19}
2025-07-14 21:42:45,924 [trainer.py] => CNN HM: [0.0, 90.471]
2025-07-14 21:42:45,924 [trainer.py] => CNN top1 curve: [92.44, 91.54]
2025-07-14 21:42:45,924 [trainer.py] => Average Accuracy (CNN): 91.99000000000001 

2025-07-14 21:42:45,925 [trainer.py] => All params: 88088289
2025-07-14 21:42:45,926 [trainer.py] => Trainable params: 2289633
2025-07-14 21:42:45,940 [ranpac.py] => Learning on 110-120
2025-07-14 21:42:48,695 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:42:48,697 [ranpac.py] => [Dynamic-K] Computed K values: [7, 12, 9, 7, 13, 13, 13, 5, 13, 10]
2025-07-14 21:42:48,697 [ranpac.py] => [KNN] task 2, dynamic K values: [7, 12, 9, 7, 13, 13, 13, 5, 13, 10]
2025-07-14 21:42:48,698 [ranpac.py] => [KNN] task 2, weight sparsity: 0.898, distance_metric: cosine
2025-07-14 21:43:11,070 [trainer.py] => No NME accuracy.
2025-07-14 21:43:11,072 [trainer.py] => CNN: {'total': 90.45, '00-99': 91.72, '100-109': 90.54, '110-119': 77.46, 'old': 91.61, 'new': 77.46}
2025-07-14 21:43:11,072 [trainer.py] => CNN HM: [0.0, 90.471, 83.943]
2025-07-14 21:43:11,072 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45]
2025-07-14 21:43:11,072 [trainer.py] => Average Accuracy (CNN): 91.47666666666667 

2025-07-14 21:43:11,074 [trainer.py] => All params: 88188289
2025-07-14 21:43:11,076 [trainer.py] => Trainable params: 2389633
2025-07-14 21:43:11,089 [ranpac.py] => Learning on 120-130
2025-07-14 21:43:13,708 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:43:13,711 [ranpac.py] => [Dynamic-K] Computed K values: [8, 8, 10, 9, 8, 4, 5, 9, 4, 3]
2025-07-14 21:43:13,711 [ranpac.py] => [KNN] task 3, dynamic K values: [8, 8, 10, 9, 8, 4, 5, 9, 4, 3]
2025-07-14 21:43:13,712 [ranpac.py] => [KNN] task 3, weight sparsity: 0.932, distance_metric: cosine
2025-07-14 21:43:36,904 [trainer.py] => No NME accuracy.
2025-07-14 21:43:36,906 [trainer.py] => CNN: {'total': 89.19, '00-99': 91.27, '100-109': 91.55, '110-119': 78.52, '120-129': 76.55, 'old': 90.25, 'new': 76.55}
2025-07-14 21:43:36,906 [trainer.py] => CNN HM: [0.0, 90.471, 83.943, 82.837]
2025-07-14 21:43:36,906 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45, 89.19]
2025-07-14 21:43:36,906 [trainer.py] => Average Accuracy (CNN): 90.905 

2025-07-14 21:43:36,908 [trainer.py] => All params: 88288289
2025-07-14 21:43:36,909 [trainer.py] => Trainable params: 2489633
2025-07-14 21:43:36,923 [ranpac.py] => Learning on 130-140
2025-07-14 21:43:39,661 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:43:39,665 [ranpac.py] => [Dynamic-K] Computed K values: [13, 9, 15, 9, 12, 5, 7, 9, 10, 6]
2025-07-14 21:43:39,666 [ranpac.py] => [KNN] task 4, dynamic K values: [13, 9, 15, 9, 12, 5, 7, 9, 10, 6]
2025-07-14 21:43:39,668 [ranpac.py] => [KNN] task 4, weight sparsity: 0.905, distance_metric: cosine
2025-07-14 21:44:08,956 [trainer.py] => No NME accuracy.
2025-07-14 21:44:08,958 [trainer.py] => CNN: {'total': 88.65, '00-99': 90.78, '100-109': 92.91, '110-119': 78.87, '120-129': 74.48, '130-139': 86.9, 'old': 88.79, 'new': 86.9}
2025-07-14 21:44:08,958 [trainer.py] => CNN HM: [0.0, 90.471, 83.943, 82.837, 87.835]
2025-07-14 21:44:08,959 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45, 89.19, 88.65]
2025-07-14 21:44:08,959 [trainer.py] => Average Accuracy (CNN): 90.454 

2025-07-14 21:44:08,961 [trainer.py] => All params: 88388289
2025-07-14 21:44:08,962 [trainer.py] => Trainable params: 2589633
2025-07-14 21:44:08,980 [ranpac.py] => Learning on 140-150
2025-07-14 21:44:12,306 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:44:12,310 [ranpac.py] => [Dynamic-K] Computed K values: [9, 19, 10, 6, 7, 13, 15, 6, 3, 12]
2025-07-14 21:44:12,312 [ranpac.py] => [KNN] task 5, dynamic K values: [9, 19, 10, 6, 7, 13, 15, 6, 3, 12]
2025-07-14 21:44:12,313 [ranpac.py] => [KNN] task 5, weight sparsity: 0.900, distance_metric: cosine
2025-07-14 21:44:32,795 [trainer.py] => No NME accuracy.
2025-07-14 21:44:32,797 [trainer.py] => CNN: {'total': 87.49, '00-99': 90.12, '100-109': 93.24, '110-119': 78.52, '120-129': 75.52, '130-139': 87.24, '140-149': 75.9, 'old': 88.28, 'new': 75.9}
2025-07-14 21:44:32,798 [trainer.py] => CNN HM: [0.0, 90.471, 83.943, 82.837, 87.835, 81.623]
2025-07-14 21:44:32,798 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45, 89.19, 88.65, 87.49]
2025-07-14 21:44:32,799 [trainer.py] => Average Accuracy (CNN): 89.96 

2025-07-14 21:44:32,800 [trainer.py] => All params: 88488289
2025-07-14 21:44:32,801 [trainer.py] => Trainable params: 2689633
2025-07-14 21:44:32,820 [ranpac.py] => Learning on 150-160
2025-07-14 21:44:37,125 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:44:37,128 [ranpac.py] => [Dynamic-K] Computed K values: [11, 10, 8, 5, 8, 6, 8, 5, 17, 11]
2025-07-14 21:44:37,129 [ranpac.py] => [KNN] task 6, dynamic K values: [11, 10, 8, 5, 8, 6, 8, 5, 17, 11]
2025-07-14 21:44:37,131 [ranpac.py] => [KNN] task 6, weight sparsity: 0.911, distance_metric: cosine
2025-07-14 21:44:58,753 [trainer.py] => No NME accuracy.
2025-07-14 21:44:58,756 [trainer.py] => CNN: {'total': 87.19, '00-99': 89.91, '100-109': 90.88, '110-119': 78.87, '120-129': 76.9, '130-139': 87.24, '140-149': 76.62, '150-159': 84.93, 'old': 87.35, 'new': 84.93}
2025-07-14 21:44:58,756 [trainer.py] => CNN HM: [0.0, 90.471, 83.943, 82.837, 87.835, 81.623, 86.123]
2025-07-14 21:44:58,756 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45, 89.19, 88.65, 87.49, 87.19]
2025-07-14 21:44:58,756 [trainer.py] => Average Accuracy (CNN): 89.56428571428572 

2025-07-14 21:44:58,757 [trainer.py] => All params: 88588289
2025-07-14 21:44:58,758 [trainer.py] => Trainable params: 2789633
2025-07-14 21:44:58,775 [ranpac.py] => Learning on 160-170
2025-07-14 21:45:01,673 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:45:01,677 [ranpac.py] => [Dynamic-K] Computed K values: [18, 13, 10, 7, 12, 10, 6, 6, 9, 11]
2025-07-14 21:45:01,678 [ranpac.py] => [KNN] task 7, dynamic K values: [18, 13, 10, 7, 12, 10, 6, 6, 9, 11]
2025-07-14 21:45:01,680 [ranpac.py] => [KNN] task 7, weight sparsity: 0.898, distance_metric: cosine
2025-07-14 21:45:30,396 [trainer.py] => No NME accuracy.
2025-07-14 21:45:30,398 [trainer.py] => CNN: {'total': 86.75, '00-99': 89.84, '100-109': 90.88, '110-119': 78.87, '120-129': 76.9, '130-139': 86.55, '140-149': 71.58, '150-159': 85.62, '160-169': 85.23, 'old': 86.85, 'new': 85.23}
2025-07-14 21:45:30,398 [trainer.py] => CNN HM: [0.0, 90.471, 83.943, 82.837, 87.835, 81.623, 86.123, 86.032]
2025-07-14 21:45:30,398 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45, 89.19, 88.65, 87.49, 87.19, 86.75]
2025-07-14 21:45:30,399 [trainer.py] => Average Accuracy (CNN): 89.2125 

2025-07-14 21:45:30,400 [trainer.py] => All params: 88688289
2025-07-14 21:45:30,400 [trainer.py] => Trainable params: 2889633
2025-07-14 21:45:30,415 [ranpac.py] => Learning on 170-180
2025-07-14 21:45:33,338 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:45:33,340 [ranpac.py] => [Dynamic-K] Computed K values: [14, 5, 11, 6, 10, 6, 7, 7, 13, 4]
2025-07-14 21:45:33,340 [ranpac.py] => [KNN] task 8, dynamic K values: [14, 5, 11, 6, 10, 6, 7, 7, 13, 4]
2025-07-14 21:45:33,341 [ranpac.py] => [KNN] task 8, weight sparsity: 0.917, distance_metric: cosine
2025-07-14 21:45:59,847 [trainer.py] => No NME accuracy.
2025-07-14 21:45:59,849 [trainer.py] => CNN: {'total': 86.3, '00-99': 89.29, '100-109': 90.88, '110-119': 78.17, '120-129': 75.52, '130-139': 87.24, '140-149': 71.58, '150-159': 84.93, '160-169': 85.57, '170-179': 85.91, 'old': 86.32, 'new': 85.91}
2025-07-14 21:45:59,849 [trainer.py] => CNN HM: [0.0, 90.471, 83.943, 82.837, 87.835, 81.623, 86.123, 86.032, 86.115]
2025-07-14 21:45:59,849 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45, 89.19, 88.65, 87.49, 87.19, 86.75, 86.3]
2025-07-14 21:45:59,849 [trainer.py] => Average Accuracy (CNN): 88.88888888888889 

2025-07-14 21:45:59,850 [trainer.py] => All params: 88788289
2025-07-14 21:45:59,851 [trainer.py] => Trainable params: 2989633
2025-07-14 21:45:59,866 [ranpac.py] => Learning on 180-190
2025-07-14 21:46:03,149 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:46:03,151 [ranpac.py] => [Dynamic-K] Computed K values: [8, 13, 3, 10, 9, 8, 11, 8, 15, 9]
2025-07-14 21:46:03,151 [ranpac.py] => [KNN] task 9, dynamic K values: [8, 13, 3, 10, 9, 8, 11, 8, 15, 9]
2025-07-14 21:46:03,151 [ranpac.py] => [KNN] task 9, weight sparsity: 0.906, distance_metric: cosine
2025-07-14 21:46:34,562 [trainer.py] => No NME accuracy.
2025-07-14 21:46:34,564 [trainer.py] => CNN: {'total': 85.5, '00-99': 88.8, '100-109': 90.2, '110-119': 78.17, '120-129': 75.52, '130-139': 85.17, '140-149': 72.66, '150-159': 84.93, '160-169': 85.23, '170-179': 84.56, '180-189': 79.44, 'old': 85.84, 'new': 79.44}
2025-07-14 21:46:34,564 [trainer.py] => CNN HM: [0.0, 90.471, 83.943, 82.837, 87.835, 81.623, 86.123, 86.032, 86.115, 82.516]
2025-07-14 21:46:34,564 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45, 89.19, 88.65, 87.49, 87.19, 86.75, 86.3, 85.5]
2025-07-14 21:46:34,564 [trainer.py] => Average Accuracy (CNN): 88.55 

2025-07-14 21:46:34,566 [trainer.py] => All params: 88888289
2025-07-14 21:46:34,566 [trainer.py] => Trainable params: 3089633
2025-07-14 21:46:34,582 [ranpac.py] => Learning on 190-200
2025-07-14 21:46:37,948 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:46:37,949 [ranpac.py] => [Dynamic-K] Computed K values: [4, 12, 8, 8, 11, 6, 10, 3, 9, 8]
2025-07-14 21:46:37,949 [ranpac.py] => [KNN] task 10, dynamic K values: [4, 12, 8, 8, 11, 6, 10, 3, 9, 8]
2025-07-14 21:46:37,950 [ranpac.py] => [KNN] task 10, weight sparsity: 0.921, distance_metric: cosine
2025-07-14 21:47:04,916 [trainer.py] => No NME accuracy.
2025-07-14 21:47:04,919 [trainer.py] => CNN: {'total': 84.97, '00-99': 88.28, '100-109': 90.88, '110-119': 78.52, '120-129': 75.86, '130-139': 85.52, '140-149': 73.38, '150-159': 85.62, '160-169': 84.23, '170-179': 84.56, '180-189': 79.44, '190-199': 78.04, 'old': 85.34, 'new': 78.04}
2025-07-14 21:47:04,919 [trainer.py] => CNN HM: [0.0, 90.471, 83.943, 82.837, 87.835, 81.623, 86.123, 86.032, 86.115, 82.516, 81.527]
2025-07-14 21:47:04,920 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.45, 89.19, 88.65, 87.49, 87.19, 86.75, 86.3, 85.5, 84.97]
2025-07-14 21:47:04,920 [trainer.py] => Average Accuracy (CNN): 88.22454545454546 

2025-07-14 21:47:04,922 [trainer.py] => Forgetting (CNN): 1.5560000000000003
2025-07-14 22:01:18,421 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/ranpac_mahalanobis.json
2025-07-14 22:01:18,424 [trainer.py] => prefix: reproduce
2025-07-14 22:01:18,424 [trainer.py] => dataset: cub
2025-07-14 22:01:18,424 [trainer.py] => memory_size: 0
2025-07-14 22:01:18,424 [trainer.py] => shuffle: True
2025-07-14 22:01:18,424 [trainer.py] => init_cls: 100
2025-07-14 22:01:18,424 [trainer.py] => increment: 10
2025-07-14 22:01:18,425 [trainer.py] => model_name: ranpac
2025-07-14 22:01:18,425 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 22:01:18,425 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 22:01:18,425 [trainer.py] => seed: 1993
2025-07-14 22:01:18,425 [trainer.py] => resume: False
2025-07-14 22:01:18,425 [trainer.py] => shot: 5
2025-07-14 22:01:18,425 [trainer.py] => use_simplecil: False
2025-07-14 22:01:18,426 [trainer.py] => tuned_epoch: 1
2025-07-14 22:01:18,426 [trainer.py] => init_lr: 0.01
2025-07-14 22:01:18,426 [trainer.py] => batch_size: 48
2025-07-14 22:01:18,426 [trainer.py] => weight_decay: 0.0005
2025-07-14 22:01:18,426 [trainer.py] => min_lr: 0
2025-07-14 22:01:18,426 [trainer.py] => ffn_num: 64
2025-07-14 22:01:18,426 [trainer.py] => optimizer: sgd
2025-07-14 22:01:18,426 [trainer.py] => use_RP: True
2025-07-14 22:01:18,427 [trainer.py] => M: 10000
2025-07-14 22:01:18,427 [trainer.py] => fecam: False
2025-07-14 22:01:18,427 [trainer.py] => calibration: True
2025-07-14 22:01:18,427 [trainer.py] => knn_k: 5
2025-07-14 22:01:18,427 [trainer.py] => knn_distance_metric: mahalanobis
2025-07-14 22:01:18,427 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 22:01:18,427 [trainer.py] => knn_adaptive_k: True
2025-07-14 22:01:18,427 [trainer.py] => knn_temperature: 16.0
2025-07-14 22:01:18,428 [trainer.py] => k_min: 3
2025-07-14 22:01:18,428 [trainer.py] => k_max: 21
2025-07-14 22:01:18,428 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-14 22:01:18,428 [trainer.py] => cosine_temperature: 16.0
2025-07-14 22:01:18,607 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 22:02:03,914 [trainer.py] => All params: 86988288
2025-07-14 22:02:03,915 [trainer.py] => Trainable params: 1189632
2025-07-14 22:02:15,368 [ranpac.py] => Learning on 0-100
2025-07-14 22:03:35,297 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-14 22:04:27,549 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-14 22:04:27,986 [ranpac.py] => [Dynamic-K] Base similarity range: [6.6863, 10.5749]
2025-07-14 22:04:27,986 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-14 22:04:27,986 [trainer.py] => No NME accuracy.
2025-07-14 22:04:27,987 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-14 22:04:27,987 [trainer.py] => CNN HM: [0.0]
2025-07-14 22:04:27,987 [trainer.py] => CNN top1 curve: [90.43]
2025-07-14 22:04:27,987 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-14 22:04:27,987 [trainer.py] => All params: 87988289
2025-07-14 22:04:27,988 [trainer.py] => Trainable params: 1189633
2025-07-14 22:04:28,059 [ranpac.py] => Learning on 100-110
2025-07-14 22:05:10,018 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:05:10,021 [ranpac.py] => [Dynamic-K] Computed K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-14 22:05:10,022 [ranpac.py] => [KNN] task 1, dynamic K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-14 22:05:10,667 [ranpac.py] => [KNN] task 1, weight sparsity: 0.891, distance_metric: mahalanobis
2025-07-14 22:05:15,798 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/ranpac_euclideanjson
2025-07-14 22:05:15,799 [trainer.py] => prefix: reproduce
2025-07-14 22:05:15,799 [trainer.py] => dataset: cub
2025-07-14 22:05:15,799 [trainer.py] => memory_size: 0
2025-07-14 22:05:15,799 [trainer.py] => shuffle: True
2025-07-14 22:05:15,799 [trainer.py] => init_cls: 100
2025-07-14 22:05:15,800 [trainer.py] => increment: 10
2025-07-14 22:05:15,800 [trainer.py] => model_name: ranpac
2025-07-14 22:05:15,800 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 22:05:15,800 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 22:05:15,800 [trainer.py] => seed: 1993
2025-07-14 22:05:15,800 [trainer.py] => resume: False
2025-07-14 22:05:15,800 [trainer.py] => shot: 5
2025-07-14 22:05:15,801 [trainer.py] => use_simplecil: False
2025-07-14 22:05:15,801 [trainer.py] => tuned_epoch: 40
2025-07-14 22:05:15,801 [trainer.py] => init_lr: 0.01
2025-07-14 22:05:15,801 [trainer.py] => batch_size: 48
2025-07-14 22:05:15,801 [trainer.py] => weight_decay: 0.0005
2025-07-14 22:05:15,801 [trainer.py] => min_lr: 0
2025-07-14 22:05:15,801 [trainer.py] => ffn_num: 64
2025-07-14 22:05:15,801 [trainer.py] => optimizer: sgd
2025-07-14 22:05:15,801 [trainer.py] => use_RP: True
2025-07-14 22:05:15,802 [trainer.py] => M: 10000
2025-07-14 22:05:15,802 [trainer.py] => fecam: False
2025-07-14 22:05:15,802 [trainer.py] => calibration: True
2025-07-14 22:05:15,802 [trainer.py] => knn_k: 5
2025-07-14 22:05:15,802 [trainer.py] => knn_distance_metric: euclidean
2025-07-14 22:05:15,802 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 22:05:15,802 [trainer.py] => knn_adaptive_k: True
2025-07-14 22:05:15,802 [trainer.py] => knn_temperature: 16.0
2025-07-14 22:05:15,802 [trainer.py] => k_min: 3
2025-07-14 22:05:15,803 [trainer.py] => k_max: 21
2025-07-14 22:05:15,803 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-14 22:05:15,803 [trainer.py] => cosine_temperature: 16.0
2025-07-14 22:05:15,917 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 22:06:21,785 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/ranpac_euclideanjson
on
2025-07-14 22:06:21,789 [trainer.py] => prefix: reproduce
2025-07-14 22:06:21,789 [trainer.py] => dataset: cub
2025-07-14 22:06:21,789 [trainer.py] => dataset: cub
2025-07-14 22:06:21,789 [trainer.py] => memory_size: 0
2025-07-14 22:06:21,790 [trainer.py] => memory_size: 0
2025-07-14 22:06:21,790 [trainer.py] => shuffle: True
2025-07-14 22:06:21,790 [trainer.py] => shuffle: True
2025-07-14 22:06:21,790 [trainer.py] => init_cls: 100
2025-07-14 22:06:21,790 [trainer.py] => init_cls: 100
2025-07-14 22:06:21,790 [trainer.py] => increment: 10
2025-07-14 22:06:21,790 [trainer.py] => increment: 10
2025-07-14 22:06:21,790 [trainer.py] => model_name: ranpac
2025-07-14 22:06:21,790 [trainer.py] => model_name: ranpac
2025-07-14 22:06:21,790 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 22:06:21,790 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 22:06:21,790 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 22:06:21,790 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 22:06:21,790 [trainer.py] => seed: 1993
2025-07-14 22:06:21,791 [trainer.py] => seed: 1993
2025-07-14 22:06:21,791 [trainer.py] => resume: False
2025-07-14 22:06:21,791 [trainer.py] => resume: False
2025-07-14 22:06:21,791 [trainer.py] => shot: 5
2025-07-14 22:06:21,791 [trainer.py] => shot: 5
2025-07-14 22:06:21,791 [trainer.py] => use_simplecil: False
2025-07-14 22:06:21,791 [trainer.py] => use_simplecil: False
2025-07-14 22:06:21,791 [trainer.py] => tuned_epoch: 40
2025-07-14 22:06:21,791 [trainer.py] => tuned_epoch: 40
2025-07-14 22:06:21,791 [trainer.py] => init_lr: 0.01
2025-07-14 22:06:21,791 [trainer.py] => init_lr: 0.01
2025-07-14 22:06:21,791 [trainer.py] => batch_size: 48
2025-07-14 22:06:21,791 [trainer.py] => batch_size: 48
2025-07-14 22:06:21,791 [trainer.py] => weight_decay: 0.0005
2025-07-14 22:06:21,791 [trainer.py] => weight_decay: 0.0005
2025-07-14 22:06:21,792 [trainer.py] => min_lr: 0
2025-07-14 22:06:21,792 [trainer.py] => min_lr: 0
2025-07-14 22:06:21,792 [trainer.py] => ffn_num: 64
2025-07-14 22:06:21,792 [trainer.py] => ffn_num: 64
2025-07-14 22:06:21,792 [trainer.py] => optimizer: sgd
2025-07-14 22:06:21,792 [trainer.py] => optimizer: sgd
2025-07-14 22:06:21,792 [trainer.py] => use_RP: True
2025-07-14 22:06:21,792 [trainer.py] => use_RP: True
2025-07-14 22:06:21,792 [trainer.py] => M: 10000
2025-07-14 22:06:21,792 [trainer.py] => M: 10000
2025-07-14 22:06:21,792 [trainer.py] => fecam: False
2025-07-14 22:06:21,792 [trainer.py] => fecam: False
2025-07-14 22:06:21,792 [trainer.py] => calibration: True
2025-07-14 22:06:21,792 [trainer.py] => calibration: True
2025-07-14 22:06:21,792 [trainer.py] => knn_k: 5
2025-07-14 22:06:21,793 [trainer.py] => knn_k: 5
2025-07-14 22:06:21,793 [trainer.py] => knn_distance_metric: euclidean
2025-07-14 22:06:21,793 [trainer.py] => knn_distance_metric: mahalanobis
2025-07-14 22:06:21,793 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 22:06:21,793 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 22:06:21,793 [trainer.py] => knn_adaptive_k: True
2025-07-14 22:06:21,793 [trainer.py] => knn_adaptive_k: True
2025-07-14 22:06:21,793 [trainer.py] => knn_temperature: 16.0
2025-07-14 22:06:21,793 [trainer.py] => knn_temperature: 16.0
2025-07-14 22:06:21,793 [trainer.py] => k_min: 3
2025-07-14 22:06:21,793 [trainer.py] => k_min: 3
2025-07-14 22:06:21,793 [trainer.py] => k_max: 21
2025-07-14 22:06:21,793 [trainer.py] => k_max: 21
2025-07-14 22:06:21,793 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-14 22:06:21,793 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-14 22:06:21,794 [trainer.py] => cosine_temperature: 16.0
2025-07-14 22:06:21,794 [trainer.py] => cosine_temperature: 16.0
2025-07-14 22:06:21,910 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 22:06:48,866 [trainer.py] => All params: 86988288
2025-07-14 22:06:48,867 [trainer.py] => Trainable params: 1189632
2025-07-14 22:06:48,867 [trainer.py] => All params: 86988288
2025-07-14 22:06:48,870 [trainer.py] => Trainable params: 1189632
2025-07-14 22:07:04,414 [ranpac.py] => Learning on 0-100
2025-07-14 22:07:04,426 [ranpac.py] => Learning on 0-100
2025-07-14 22:24:58,628 [ranpac.py] => Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48
2025-07-14 22:24:58,654 [ranpac.py] => Task 0, Epoch 40/40 => Loss 0.378, Train_accy 92.66, Test_accy 92.48
2025-07-14 22:25:28,450 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-14 22:25:28,572 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-14 22:25:28,905 [ranpac.py] => [Dynamic-K] Base similarity range: [-0.0264, 0.8900]
2025-07-14 22:25:28,905 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-14 22:25:28,905 [trainer.py] => No NME accuracy.
2025-07-14 22:25:28,905 [trainer.py] => CNN: {'total': 92.44, '00-99': 92.44, 'old': 0, 'new': 92.44}
2025-07-14 22:25:28,905 [trainer.py] => CNN HM: [0.0]
2025-07-14 22:25:28,906 [trainer.py] => CNN top1 curve: [92.44]
2025-07-14 22:25:28,906 [trainer.py] => Average Accuracy (CNN): 92.44 

2025-07-14 22:25:28,906 [trainer.py] => All params: 87988289
2025-07-14 22:25:28,907 [trainer.py] => Trainable params: 1189633
2025-07-14 22:25:29,005 [ranpac.py] => Learning on 100-110
2025-07-14 22:25:29,029 [ranpac.py] => [Dynamic-K] Base similarity range: [-0.0264, 0.8900]
2025-07-14 22:25:29,029 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-14 22:25:29,029 [trainer.py] => No NME accuracy.
2025-07-14 22:25:29,029 [trainer.py] => CNN: {'total': 92.44, '00-99': 92.44, 'old': 0, 'new': 92.44}
2025-07-14 22:25:29,029 [trainer.py] => CNN HM: [0.0]
2025-07-14 22:25:29,029 [trainer.py] => CNN top1 curve: [92.44]
2025-07-14 22:25:29,029 [trainer.py] => Average Accuracy (CNN): 92.44 

2025-07-14 22:25:29,030 [trainer.py] => All params: 87988289
2025-07-14 22:25:29,030 [trainer.py] => Trainable params: 1189633
2025-07-14 22:25:29,039 [ranpac.py] => Learning on 100-110
2025-07-14 22:25:32,003 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:25:32,036 [ranpac.py] => [Dynamic-K] Computed K values: [13, 14, 7, 14, 7, 7, 13, 10, 12, 8]
2025-07-14 22:25:32,036 [ranpac.py] => [KNN] task 1, dynamic K values: [13, 14, 7, 14, 7, 7, 13, 10, 12, 8]
2025-07-14 22:25:32,200 [ranpac.py] => [KNN] task 1, weight sparsity: 0.908, distance_metric: euclidean
2025-07-14 22:25:54,211 [trainer.py] => No NME accuracy.
2025-07-14 22:25:54,211 [trainer.py] => CNN: {'total': 91.54, '00-99': 91.79, '100-109': 89.19, 'old': 91.79, 'new': 89.19}
2025-07-14 22:25:54,211 [trainer.py] => CNN HM: [0.0, 90.471]
2025-07-14 22:25:54,211 [trainer.py] => CNN top1 curve: [92.44, 91.54]
2025-07-14 22:25:54,211 [trainer.py] => Average Accuracy (CNN): 91.99000000000001 

2025-07-14 22:25:54,212 [trainer.py] => All params: 88088289
2025-07-14 22:25:54,213 [trainer.py] => Trainable params: 2289633
2025-07-14 22:25:54,225 [ranpac.py] => Learning on 110-120
2025-07-14 22:25:57,211 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:25:57,213 [ranpac.py] => [Dynamic-K] Computed K values: [7, 12, 9, 7, 13, 13, 13, 5, 13, 10]
2025-07-14 22:25:57,213 [ranpac.py] => [KNN] task 2, dynamic K values: [7, 12, 9, 7, 13, 13, 13, 5, 13, 10]
2025-07-14 22:25:57,213 [ranpac.py] => [KNN] task 2, weight sparsity: 0.910, distance_metric: euclidean
2025-07-14 22:26:09,298 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:26:09,302 [ranpac.py] => [Dynamic-K] Computed K values: [13, 14, 7, 14, 7, 7, 13, 10, 12, 8]
2025-07-14 22:26:09,302 [ranpac.py] => [KNN] task 1, dynamic K values: [13, 14, 7, 14, 7, 7, 13, 10, 12, 8]
2025-07-14 22:26:09,304 [ranpac.py] => [KNN] task 1, weight sparsity: 0.895, distance_metric: mahalanobis
2025-07-14 22:26:13,150 [trainer.py] => No NME accuracy.
2025-07-14 22:26:13,150 [trainer.py] => CNN: {'total': 90.53, '00-99': 91.85, '100-109': 89.86, '110-119': 77.82, 'old': 91.67, 'new': 77.82}
2025-07-14 22:26:13,151 [trainer.py] => CNN HM: [0.0, 90.471, 84.179]
2025-07-14 22:26:13,151 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53]
2025-07-14 22:26:13,151 [trainer.py] => Average Accuracy (CNN): 91.50333333333333 

2025-07-14 22:26:13,152 [trainer.py] => All params: 88188289
2025-07-14 22:26:13,153 [trainer.py] => Trainable params: 2389633
2025-07-14 22:26:13,170 [ranpac.py] => Learning on 120-130
2025-07-14 22:26:16,242 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:26:16,245 [ranpac.py] => [Dynamic-K] Computed K values: [8, 8, 10, 9, 8, 4, 5, 9, 4, 3]
2025-07-14 22:26:16,246 [ranpac.py] => [KNN] task 3, dynamic K values: [8, 8, 10, 9, 8, 4, 5, 9, 4, 3]
2025-07-14 22:26:16,247 [ranpac.py] => [KNN] task 3, weight sparsity: 0.945, distance_metric: euclidean
2025-07-14 22:26:29,152 [trainer.py] => No NME accuracy.
2025-07-14 22:26:29,154 [trainer.py] => CNN: {'total': 91.45, '00-99': 91.65, '100-109': 89.53, 'old': 91.65, 'new': 89.53}
2025-07-14 22:26:29,155 [trainer.py] => CNN HM: [0.0, 90.578]
2025-07-14 22:26:29,155 [trainer.py] => CNN top1 curve: [92.44, 91.45]
2025-07-14 22:26:29,156 [trainer.py] => Average Accuracy (CNN): 91.945 

2025-07-14 22:26:29,157 [trainer.py] => All params: 88088289
2025-07-14 22:26:29,159 [trainer.py] => Trainable params: 2289633
2025-07-14 22:26:29,175 [ranpac.py] => Learning on 110-120
2025-07-14 22:26:31,822 [trainer.py] => No NME accuracy.
2025-07-14 22:26:31,823 [trainer.py] => CNN: {'total': 89.16, '00-99': 91.09, '100-109': 91.22, '110-119': 79.23, '120-129': 77.59, 'old': 90.13, 'new': 77.59}
2025-07-14 22:26:31,824 [trainer.py] => CNN HM: [0.0, 90.471, 84.179, 83.391]
2025-07-14 22:26:31,824 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53, 89.16]
2025-07-14 22:26:31,825 [trainer.py] => Average Accuracy (CNN): 90.91749999999999 

2025-07-14 22:26:31,826 [trainer.py] => All params: 88288289
2025-07-14 22:26:31,827 [trainer.py] => Trainable params: 2489633
2025-07-14 22:26:31,842 [ranpac.py] => Learning on 130-140
2025-07-14 22:26:35,190 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:26:35,193 [ranpac.py] => [Dynamic-K] Computed K values: [13, 9, 15, 9, 12, 5, 7, 9, 10, 6]
2025-07-14 22:26:35,193 [ranpac.py] => [KNN] task 4, dynamic K values: [13, 9, 15, 9, 12, 5, 7, 9, 10, 6]
2025-07-14 22:26:35,194 [ranpac.py] => [KNN] task 4, weight sparsity: 0.915, distance_metric: euclidean
2025-07-14 22:26:52,772 [trainer.py] => No NME accuracy.
2025-07-14 22:26:52,773 [trainer.py] => CNN: {'total': 88.73, '00-99': 90.64, '100-109': 91.55, '110-119': 79.93, '120-129': 76.55, '130-139': 87.59, 'old': 88.81, 'new': 87.59}
2025-07-14 22:26:52,773 [trainer.py] => CNN HM: [0.0, 90.471, 84.179, 83.391, 88.196]
2025-07-14 22:26:52,773 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53, 89.16, 88.73]
2025-07-14 22:26:52,774 [trainer.py] => Average Accuracy (CNN): 90.47999999999999 

2025-07-14 22:26:52,775 [trainer.py] => All params: 88388289
2025-07-14 22:26:52,777 [trainer.py] => Trainable params: 2589633
2025-07-14 22:26:52,790 [ranpac.py] => Learning on 140-150
2025-07-14 22:26:56,497 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:26:56,499 [ranpac.py] => [Dynamic-K] Computed K values: [9, 19, 10, 6, 7, 13, 15, 6, 3, 12]
2025-07-14 22:26:56,500 [ranpac.py] => [KNN] task 5, dynamic K values: [9, 19, 10, 6, 7, 13, 15, 6, 3, 12]
2025-07-14 22:26:56,500 [ranpac.py] => [KNN] task 5, weight sparsity: 0.912, distance_metric: euclidean
2025-07-14 22:27:07,214 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:27:07,217 [ranpac.py] => [Dynamic-K] Computed K values: [7, 12, 9, 7, 13, 13, 13, 5, 13, 10]
2025-07-14 22:27:07,217 [ranpac.py] => [KNN] task 2, dynamic K values: [7, 12, 9, 7, 13, 13, 13, 5, 13, 10]
2025-07-14 22:27:07,218 [ranpac.py] => [KNN] task 2, weight sparsity: 0.898, distance_metric: mahalanobis
2025-07-14 22:27:22,343 [trainer.py] => No NME accuracy.
2025-07-14 22:27:22,346 [trainer.py] => CNN: {'total': 90.04, '00-99': 91.3, '100-109': 90.2, '110-119': 77.11, 'old': 91.2, 'new': 77.11}
2025-07-14 22:27:22,346 [trainer.py] => CNN HM: [0.0, 90.578, 83.565]
2025-07-14 22:27:22,346 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04]
2025-07-14 22:27:22,346 [trainer.py] => Average Accuracy (CNN): 91.31 

2025-07-14 22:27:22,348 [trainer.py] => All params: 88188289
2025-07-14 22:27:22,350 [trainer.py] => Trainable params: 2389633
2025-07-14 22:27:22,364 [ranpac.py] => Learning on 120-130
2025-07-14 22:27:26,543 [trainer.py] => No NME accuracy.
2025-07-14 22:27:26,544 [trainer.py] => CNN: {'total': 87.49, '00-99': 90.19, '100-109': 92.23, '110-119': 77.11, '120-129': 76.9, '130-139': 87.93, '140-149': 75.54, 'old': 88.31, 'new': 75.54}
2025-07-14 22:27:26,544 [trainer.py] => CNN HM: [0.0, 90.471, 84.179, 83.391, 88.196, 81.427]
2025-07-14 22:27:26,544 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53, 89.16, 88.73, 87.49]
2025-07-14 22:27:26,544 [trainer.py] => Average Accuracy (CNN): 89.98166666666667 

2025-07-14 22:27:26,545 [trainer.py] => All params: 88488289
2025-07-14 22:27:26,545 [trainer.py] => Trainable params: 2689633
2025-07-14 22:27:26,559 [ranpac.py] => Learning on 150-160
2025-07-14 22:27:28,988 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:27:28,990 [ranpac.py] => [Dynamic-K] Computed K values: [11, 10, 8, 5, 8, 6, 8, 5, 17, 11]
2025-07-14 22:27:28,990 [ranpac.py] => [KNN] task 6, dynamic K values: [11, 10, 8, 5, 8, 6, 8, 5, 17, 11]
2025-07-14 22:27:28,991 [ranpac.py] => [KNN] task 6, weight sparsity: 0.924, distance_metric: euclidean
2025-07-14 22:27:45,365 [trainer.py] => No NME accuracy.
2025-07-14 22:27:45,365 [trainer.py] => CNN: {'total': 87.17, '00-99': 90.16, '100-109': 90.54, '110-119': 78.87, '120-129': 76.55, '130-139': 87.59, '140-149': 74.82, '150-159': 84.25, 'old': 87.37, 'new': 84.25}
2025-07-14 22:27:45,365 [trainer.py] => CNN HM: [0.0, 90.471, 84.179, 83.391, 88.196, 81.427, 85.782]
2025-07-14 22:27:45,365 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53, 89.16, 88.73, 87.49, 87.17]
2025-07-14 22:27:45,365 [trainer.py] => Average Accuracy (CNN): 89.58 

2025-07-14 22:27:45,366 [trainer.py] => All params: 88588289
2025-07-14 22:27:45,367 [trainer.py] => Trainable params: 2789633
2025-07-14 22:27:45,382 [ranpac.py] => Learning on 160-170
2025-07-14 22:27:49,128 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:27:49,129 [ranpac.py] => [Dynamic-K] Computed K values: [18, 13, 10, 7, 12, 10, 6, 6, 9, 11]
2025-07-14 22:27:49,129 [ranpac.py] => [KNN] task 7, dynamic K values: [18, 13, 10, 7, 12, 10, 6, 6, 9, 11]
2025-07-14 22:27:49,130 [ranpac.py] => [KNN] task 7, weight sparsity: 0.911, distance_metric: euclidean
2025-07-14 22:28:00,817 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:28:00,820 [ranpac.py] => [Dynamic-K] Computed K values: [8, 8, 10, 9, 8, 4, 5, 9, 4, 3]
2025-07-14 22:28:00,820 [ranpac.py] => [KNN] task 3, dynamic K values: [8, 8, 10, 9, 8, 4, 5, 9, 4, 3]
2025-07-14 22:28:00,821 [ranpac.py] => [KNN] task 3, weight sparsity: 0.932, distance_metric: mahalanobis
2025-07-14 22:28:04,535 [trainer.py] => No NME accuracy.
2025-07-14 22:28:04,535 [trainer.py] => CNN: {'total': 86.48, '00-99': 89.81, '100-109': 90.88, '110-119': 77.46, '120-129': 76.21, '130-139': 87.24, '140-149': 69.42, '150-159': 85.27, '160-169': 84.9, 'old': 86.59, 'new': 84.9}
2025-07-14 22:28:04,536 [trainer.py] => CNN HM: [0.0, 90.471, 84.179, 83.391, 88.196, 81.427, 85.782, 85.737]
2025-07-14 22:28:04,536 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53, 89.16, 88.73, 87.49, 87.17, 86.48]
2025-07-14 22:28:04,536 [trainer.py] => Average Accuracy (CNN): 89.1925 

2025-07-14 22:28:04,538 [trainer.py] => All params: 88688289
2025-07-14 22:28:04,539 [trainer.py] => Trainable params: 2889633
2025-07-14 22:28:04,553 [ranpac.py] => Learning on 170-180
2025-07-14 22:28:07,227 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:28:07,229 [ranpac.py] => [Dynamic-K] Computed K values: [14, 5, 11, 6, 10, 6, 7, 7, 13, 4]
2025-07-14 22:28:07,229 [ranpac.py] => [KNN] task 8, dynamic K values: [14, 5, 11, 6, 10, 6, 7, 7, 13, 4]
2025-07-14 22:28:07,230 [ranpac.py] => [KNN] task 8, weight sparsity: 0.928, distance_metric: euclidean
2025-07-14 22:28:13,145 [trainer.py] => No NME accuracy.
2025-07-14 22:28:13,148 [trainer.py] => CNN: {'total': 88.76, '00-99': 90.95, '100-109': 90.88, '110-119': 77.46, '120-129': 75.86, 'old': 89.84, 'new': 75.86}
2025-07-14 22:28:13,149 [trainer.py] => CNN HM: [0.0, 90.578, 83.565, 82.26]
2025-07-14 22:28:13,149 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04, 88.76]
2025-07-14 22:28:13,149 [trainer.py] => Average Accuracy (CNN): 90.6725 

2025-07-14 22:28:13,151 [trainer.py] => All params: 88288289
2025-07-14 22:28:13,153 [trainer.py] => Trainable params: 2489633
2025-07-14 22:28:13,169 [ranpac.py] => Learning on 130-140
2025-07-14 22:28:22,785 [trainer.py] => No NME accuracy.
2025-07-14 22:28:22,785 [trainer.py] => CNN: {'total': 86.13, '00-99': 89.39, '100-109': 90.54, '110-119': 77.11, '120-129': 75.17, '130-139': 86.9, '140-149': 71.22, '150-159': 84.93, '160-169': 84.56, '170-179': 85.23, 'old': 86.18, 'new': 85.23}
2025-07-14 22:28:22,785 [trainer.py] => CNN HM: [0.0, 90.471, 84.179, 83.391, 88.196, 81.427, 85.782, 85.737, 85.702]
2025-07-14 22:28:22,785 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53, 89.16, 88.73, 87.49, 87.17, 86.48, 86.13]
2025-07-14 22:28:22,785 [trainer.py] => Average Accuracy (CNN): 88.85222222222222 

2025-07-14 22:28:22,786 [trainer.py] => All params: 88788289
2025-07-14 22:28:22,787 [trainer.py] => Trainable params: 2989633
2025-07-14 22:28:22,803 [ranpac.py] => Learning on 180-190
2025-07-14 22:28:25,484 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:28:25,486 [ranpac.py] => [Dynamic-K] Computed K values: [8, 13, 3, 10, 9, 8, 11, 8, 15, 9]
2025-07-14 22:28:25,486 [ranpac.py] => [KNN] task 9, dynamic K values: [8, 13, 3, 10, 9, 8, 11, 8, 15, 9]
2025-07-14 22:28:25,486 [ranpac.py] => [KNN] task 9, weight sparsity: 0.918, distance_metric: euclidean
2025-07-14 22:28:44,132 [trainer.py] => No NME accuracy.
2025-07-14 22:28:44,133 [trainer.py] => CNN: {'total': 85.45, '00-99': 89.08, '100-109': 90.54, '110-119': 77.11, '120-129': 75.17, '130-139': 86.21, '140-149': 70.86, '150-159': 84.59, '160-169': 83.89, '170-179': 84.56, '180-189': 79.09, 'old': 85.8, 'new': 79.09}
2025-07-14 22:28:44,133 [trainer.py] => CNN HM: [0.0, 90.471, 84.179, 83.391, 88.196, 81.427, 85.782, 85.737, 85.702, 82.308]
2025-07-14 22:28:44,133 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53, 89.16, 88.73, 87.49, 87.17, 86.48, 86.13, 85.45]
2025-07-14 22:28:44,133 [trainer.py] => Average Accuracy (CNN): 88.512 

2025-07-14 22:28:44,134 [trainer.py] => All params: 88888289
2025-07-14 22:28:44,134 [trainer.py] => Trainable params: 3089633
2025-07-14 22:28:44,148 [ranpac.py] => Learning on 190-200
2025-07-14 22:28:47,062 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:28:47,063 [ranpac.py] => [Dynamic-K] Computed K values: [4, 12, 8, 8, 11, 6, 10, 3, 9, 8]
2025-07-14 22:28:47,063 [ranpac.py] => [KNN] task 10, dynamic K values: [4, 12, 8, 8, 11, 6, 10, 3, 9, 8]
2025-07-14 22:28:47,064 [ranpac.py] => [KNN] task 10, weight sparsity: 0.932, distance_metric: euclidean
2025-07-14 22:28:50,325 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:28:50,329 [ranpac.py] => [Dynamic-K] Computed K values: [13, 9, 15, 9, 12, 5, 7, 9, 10, 6]
2025-07-14 22:28:50,329 [ranpac.py] => [KNN] task 4, dynamic K values: [13, 9, 15, 9, 12, 5, 7, 9, 10, 6]
2025-07-14 22:28:50,330 [ranpac.py] => [KNN] task 4, weight sparsity: 0.905, distance_metric: mahalanobis
2025-07-14 22:29:04,953 [trainer.py] => No NME accuracy.
2025-07-14 22:29:04,955 [trainer.py] => CNN: {'total': 88.48, '00-99': 90.68, '100-109': 91.22, '110-119': 79.23, '120-129': 73.79, '130-139': 87.59, 'old': 88.55, 'new': 87.59}
2025-07-14 22:29:04,955 [trainer.py] => CNN HM: [0.0, 90.578, 83.565, 82.26, 88.067]
2025-07-14 22:29:04,955 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04, 88.76, 88.48]
2025-07-14 22:29:04,955 [trainer.py] => Average Accuracy (CNN): 90.23400000000001 

2025-07-14 22:29:04,956 [trainer.py] => All params: 88388289
2025-07-14 22:29:04,957 [trainer.py] => Trainable params: 2589633
2025-07-14 22:29:04,971 [ranpac.py] => Learning on 140-150
2025-07-14 22:29:07,736 [trainer.py] => No NME accuracy.
2025-07-14 22:29:07,736 [trainer.py] => CNN: {'total': 84.67, '00-99': 88.53, '100-109': 90.54, '110-119': 77.82, '120-129': 75.17, '130-139': 86.55, '140-149': 71.58, '150-159': 84.59, '160-169': 82.55, '170-179': 84.9, '180-189': 78.4, '190-199': 75.68, 'old': 85.16, 'new': 75.68}
2025-07-14 22:29:07,736 [trainer.py] => CNN HM: [0.0, 90.471, 84.179, 83.391, 88.196, 81.427, 85.782, 85.737, 85.702, 82.308, 80.141]
2025-07-14 22:29:07,736 [trainer.py] => CNN top1 curve: [92.44, 91.54, 90.53, 89.16, 88.73, 87.49, 87.17, 86.48, 86.13, 85.45, 84.67]
2025-07-14 22:29:07,736 [trainer.py] => Average Accuracy (CNN): 88.16272727272727 

2025-07-14 22:29:07,737 [trainer.py] => Forgetting (CNN): 1.9520000000000024
2025-07-14 22:29:43,359 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:29:43,362 [ranpac.py] => [Dynamic-K] Computed K values: [9, 19, 10, 6, 7, 13, 15, 6, 3, 12]
2025-07-14 22:29:43,362 [ranpac.py] => [KNN] task 5, dynamic K values: [9, 19, 10, 6, 7, 13, 15, 6, 3, 12]
2025-07-14 22:29:43,363 [ranpac.py] => [KNN] task 5, weight sparsity: 0.900, distance_metric: mahalanobis
2025-07-14 22:30:10,696 [trainer.py] => No NME accuracy.
2025-07-14 22:30:10,698 [trainer.py] => CNN: {'total': 87.23, '00-99': 90.47, '100-109': 91.55, '110-119': 76.41, '120-129': 73.45, '130-139': 87.24, '140-149': 74.46, 'old': 88.11, 'new': 74.46}
2025-07-14 22:30:10,698 [trainer.py] => CNN HM: [0.0, 90.578, 83.565, 82.26, 88.067, 80.712]
2025-07-14 22:30:10,698 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04, 88.76, 88.48, 87.23]
2025-07-14 22:30:10,699 [trainer.py] => Average Accuracy (CNN): 89.73333333333333 

2025-07-14 22:30:10,700 [trainer.py] => All params: 88488289
2025-07-14 22:30:10,701 [trainer.py] => Trainable params: 2689633
2025-07-14 22:30:10,718 [ranpac.py] => Learning on 150-160
2025-07-14 22:30:48,426 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:30:48,430 [ranpac.py] => [Dynamic-K] Computed K values: [11, 10, 8, 5, 8, 6, 8, 5, 17, 11]
2025-07-14 22:30:48,430 [ranpac.py] => [KNN] task 6, dynamic K values: [11, 10, 8, 5, 8, 6, 8, 5, 17, 11]
2025-07-14 22:30:48,430 [ranpac.py] => [KNN] task 6, weight sparsity: 0.911, distance_metric: mahalanobis
2025-07-14 22:31:17,741 [trainer.py] => No NME accuracy.
2025-07-14 22:31:17,743 [trainer.py] => CNN: {'total': 86.76, '00-99': 90.16, '100-109': 89.86, '110-119': 77.82, '120-129': 72.76, '130-139': 86.9, '140-149': 75.18, '150-159': 83.56, 'old': 86.98, 'new': 83.56}
2025-07-14 22:31:17,743 [trainer.py] => CNN HM: [0.0, 90.578, 83.565, 82.26, 88.067, 80.712, 85.236]
2025-07-14 22:31:17,744 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04, 88.76, 88.48, 87.23, 86.76]
2025-07-14 22:31:17,745 [trainer.py] => Average Accuracy (CNN): 89.30857142857143 

2025-07-14 22:31:17,746 [trainer.py] => All params: 88588289
2025-07-14 22:31:17,748 [trainer.py] => Trainable params: 2789633
2025-07-14 22:31:17,767 [ranpac.py] => Learning on 160-170
2025-07-14 22:31:54,325 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:31:54,329 [ranpac.py] => [Dynamic-K] Computed K values: [18, 13, 10, 7, 12, 10, 6, 6, 9, 11]
2025-07-14 22:31:54,329 [ranpac.py] => [KNN] task 7, dynamic K values: [18, 13, 10, 7, 12, 10, 6, 6, 9, 11]
2025-07-14 22:31:54,330 [ranpac.py] => [KNN] task 7, weight sparsity: 0.898, distance_metric: mahalanobis
2025-07-14 22:32:10,383 [trainer.py] => No NME accuracy.
2025-07-14 22:32:10,385 [trainer.py] => CNN: {'total': 86.0, '00-99': 89.57, '100-109': 90.2, '110-119': 76.76, '120-129': 73.79, '130-139': 86.21, '140-149': 70.14, '150-159': 83.9, '160-169': 84.56, 'old': 86.09, 'new': 84.56}
2025-07-14 22:32:10,386 [trainer.py] => CNN HM: [0.0, 90.578, 83.565, 82.26, 88.067, 80.712, 85.236, 85.318]
2025-07-14 22:32:10,387 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04, 88.76, 88.48, 87.23, 86.76, 86.0]
2025-07-14 22:32:10,387 [trainer.py] => Average Accuracy (CNN): 88.895 

2025-07-14 22:32:10,388 [trainer.py] => All params: 88688289
2025-07-14 22:32:10,389 [trainer.py] => Trainable params: 2889633
2025-07-14 22:32:10,408 [ranpac.py] => Learning on 170-180
2025-07-14 22:32:49,121 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:32:49,124 [ranpac.py] => [Dynamic-K] Computed K values: [14, 5, 11, 6, 10, 6, 7, 7, 13, 4]
2025-07-14 22:32:49,125 [ranpac.py] => [KNN] task 8, dynamic K values: [14, 5, 11, 6, 10, 6, 7, 7, 13, 4]
2025-07-14 22:32:49,126 [ranpac.py] => [KNN] task 8, weight sparsity: 0.917, distance_metric: mahalanobis
2025-07-14 22:33:21,655 [trainer.py] => No NME accuracy.
2025-07-14 22:33:21,657 [trainer.py] => CNN: {'total': 85.74, '00-99': 89.36, '100-109': 90.2, '110-119': 76.06, '120-129': 73.79, '130-139': 86.21, '140-149': 69.78, '150-159': 83.22, '160-169': 84.23, '170-179': 85.57, 'old': 85.75, 'new': 85.57}
2025-07-14 22:33:21,658 [trainer.py] => CNN HM: [0.0, 90.578, 83.565, 82.26, 88.067, 80.712, 85.236, 85.318, 85.66]
2025-07-14 22:33:21,658 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04, 88.76, 88.48, 87.23, 86.76, 86.0, 85.74]
2025-07-14 22:33:21,659 [trainer.py] => Average Accuracy (CNN): 88.54444444444444 

2025-07-14 22:33:21,660 [trainer.py] => All params: 88788289
2025-07-14 22:33:21,661 [trainer.py] => Trainable params: 2989633
2025-07-14 22:33:21,679 [ranpac.py] => Learning on 180-190
2025-07-14 22:33:58,111 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:33:58,115 [ranpac.py] => [Dynamic-K] Computed K values: [8, 13, 3, 10, 9, 8, 11, 8, 15, 9]
2025-07-14 22:33:58,115 [ranpac.py] => [KNN] task 9, dynamic K values: [8, 13, 3, 10, 9, 8, 11, 8, 15, 9]
2025-07-14 22:33:58,116 [ranpac.py] => [KNN] task 9, weight sparsity: 0.906, distance_metric: mahalanobis
2025-07-14 22:34:32,536 [trainer.py] => No NME accuracy.
2025-07-14 22:34:32,538 [trainer.py] => CNN: {'total': 85.3, '00-99': 89.32, '100-109': 90.2, '110-119': 76.06, '120-129': 73.79, '130-139': 85.17, '140-149': 71.22, '150-159': 83.56, '160-169': 83.89, '170-179': 84.56, '180-189': 78.4, 'old': 85.68, 'new': 78.4}
2025-07-14 22:34:32,538 [trainer.py] => CNN HM: [0.0, 90.578, 83.565, 82.26, 88.067, 80.712, 85.236, 85.318, 85.66, 81.878]
2025-07-14 22:34:32,538 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04, 88.76, 88.48, 87.23, 86.76, 86.0, 85.74, 85.3]
2025-07-14 22:34:32,538 [trainer.py] => Average Accuracy (CNN): 88.22 

2025-07-14 22:34:32,539 [trainer.py] => All params: 88888289
2025-07-14 22:34:32,540 [trainer.py] => Trainable params: 3089633
2025-07-14 22:34:32,556 [ranpac.py] => Learning on 190-200
2025-07-14 22:35:08,237 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:35:08,238 [ranpac.py] => [Dynamic-K] Computed K values: [4, 12, 8, 8, 11, 6, 10, 3, 9, 8]
2025-07-14 22:35:08,239 [ranpac.py] => [KNN] task 10, dynamic K values: [4, 12, 8, 8, 11, 6, 10, 3, 9, 8]
2025-07-14 22:35:08,239 [ranpac.py] => [KNN] task 10, weight sparsity: 0.921, distance_metric: mahalanobis
2025-07-14 22:35:27,523 [trainer.py] => No NME accuracy.
2025-07-14 22:35:27,524 [trainer.py] => CNN: {'total': 84.47, '00-99': 88.77, '100-109': 89.86, '110-119': 76.76, '120-129': 73.79, '130-139': 84.83, '140-149': 70.86, '150-159': 83.9, '160-169': 82.89, '170-179': 84.23, '180-189': 77.35, '190-199': 76.69, 'old': 84.89, 'new': 76.69}
2025-07-14 22:35:27,524 [trainer.py] => CNN HM: [0.0, 90.578, 83.565, 82.26, 88.067, 80.712, 85.236, 85.318, 85.66, 81.878, 80.582]
2025-07-14 22:35:27,524 [trainer.py] => CNN top1 curve: [92.44, 91.45, 90.04, 88.76, 88.48, 87.23, 86.76, 86.0, 85.74, 85.3, 84.47]
2025-07-14 22:35:27,524 [trainer.py] => Average Accuracy (CNN): 87.8790909090909 

2025-07-14 22:35:27,525 [trainer.py] => Forgetting (CNN): 2.1040000000000005
