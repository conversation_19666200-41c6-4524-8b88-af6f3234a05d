2025-07-14 22:01:18,421 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/ranpac_mahalanobis.json
2025-07-14 22:01:18,424 [trainer.py] => prefix: reproduce
2025-07-14 22:01:18,424 [trainer.py] => dataset: cub
2025-07-14 22:01:18,424 [trainer.py] => memory_size: 0
2025-07-14 22:01:18,424 [trainer.py] => shuffle: True
2025-07-14 22:01:18,424 [trainer.py] => init_cls: 100
2025-07-14 22:01:18,424 [trainer.py] => increment: 10
2025-07-14 22:01:18,425 [trainer.py] => model_name: ranpac
2025-07-14 22:01:18,425 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 22:01:18,425 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 22:01:18,425 [trainer.py] => seed: 1993
2025-07-14 22:01:18,425 [trainer.py] => resume: False
2025-07-14 22:01:18,425 [trainer.py] => shot: 5
2025-07-14 22:01:18,425 [trainer.py] => use_simplecil: False
2025-07-14 22:01:18,426 [trainer.py] => tuned_epoch: 1
2025-07-14 22:01:18,426 [trainer.py] => init_lr: 0.01
2025-07-14 22:01:18,426 [trainer.py] => batch_size: 48
2025-07-14 22:01:18,426 [trainer.py] => weight_decay: 0.0005
2025-07-14 22:01:18,426 [trainer.py] => min_lr: 0
2025-07-14 22:01:18,426 [trainer.py] => ffn_num: 64
2025-07-14 22:01:18,426 [trainer.py] => optimizer: sgd
2025-07-14 22:01:18,426 [trainer.py] => use_RP: True
2025-07-14 22:01:18,427 [trainer.py] => M: 10000
2025-07-14 22:01:18,427 [trainer.py] => fecam: False
2025-07-14 22:01:18,427 [trainer.py] => calibration: True
2025-07-14 22:01:18,427 [trainer.py] => knn_k: 5
2025-07-14 22:01:18,427 [trainer.py] => knn_distance_metric: mahalanobis
2025-07-14 22:01:18,427 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 22:01:18,427 [trainer.py] => knn_adaptive_k: True
2025-07-14 22:01:18,427 [trainer.py] => knn_temperature: 16.0
2025-07-14 22:01:18,428 [trainer.py] => k_min: 3
2025-07-14 22:01:18,428 [trainer.py] => k_max: 21
2025-07-14 22:01:18,428 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-14 22:01:18,428 [trainer.py] => cosine_temperature: 16.0
2025-07-14 22:01:18,607 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
This is for the BaseNet initialization.
I'm using ViT with adapters.
_IncompatibleKeys(missing_keys=['blocks.0.adaptmlp.down_proj.weight', 'blocks.0.adaptmlp.down_proj.bias', 'blocks.0.adaptmlp.up_proj.weight', 'blocks.0.adaptmlp.up_proj.bias', 'blocks.1.adaptmlp.down_proj.weight', 'blocks.1.adaptmlp.down_proj.bias', 'blocks.1.adaptmlp.up_proj.weight', 'blocks.1.adaptmlp.up_proj.bias', 'blocks.2.adaptmlp.down_proj.weight', 'blocks.2.adaptmlp.down_proj.bias', 'blocks.2.adaptmlp.up_proj.weight', 'blocks.2.adaptmlp.up_proj.bias', 'blocks.3.adaptmlp.down_proj.weight', 'blocks.3.adaptmlp.down_proj.bias', 'blocks.3.adaptmlp.up_proj.weight', 'blocks.3.adaptmlp.up_proj.bias', 'blocks.4.adaptmlp.down_proj.weight', 'blocks.4.adaptmlp.down_proj.bias', 'blocks.4.adaptmlp.up_proj.weight', 'blocks.4.adaptmlp.up_proj.bias', 'blocks.5.adaptmlp.down_proj.weight', 'blocks.5.adaptmlp.down_proj.bias', 'blocks.5.adaptmlp.up_proj.weight', 'blocks.5.adaptmlp.up_proj.bias', 'blocks.6.adaptmlp.down_proj.weight', 'blocks.6.adaptmlp.down_proj.bias', 'blocks.6.adaptmlp.up_proj.weight', 'blocks.6.adaptmlp.up_proj.bias', 'blocks.7.adaptmlp.down_proj.weight', 'blocks.7.adaptmlp.down_proj.bias', 'blocks.7.adaptmlp.up_proj.weight', 'blocks.7.adaptmlp.up_proj.bias', 'blocks.8.adaptmlp.down_proj.weight', 'blocks.8.adaptmlp.down_proj.bias', 'blocks.8.adaptmlp.up_proj.weight', 'blocks.8.adaptmlp.up_proj.bias', 'blocks.9.adaptmlp.down_proj.weight', 'blocks.9.adaptmlp.down_proj.bias', 'blocks.9.adaptmlp.up_proj.weight', 'blocks.9.adaptmlp.up_proj.bias', 'blocks.10.adaptmlp.down_proj.weight', 'blocks.10.adaptmlp.down_proj.bias', 'blocks.10.adaptmlp.up_proj.weight', 'blocks.10.adaptmlp.up_proj.bias', 'blocks.11.adaptmlp.down_proj.weight', 'blocks.11.adaptmlp.down_proj.bias', 'blocks.11.adaptmlp.up_proj.weight', 'blocks.11.adaptmlp.up_proj.bias'], unexpected_keys=[])
After BaseNet initialization.
2025-07-14 22:02:03,914 [trainer.py] => All params: 86988288
2025-07-14 22:02:03,915 [trainer.py] => Trainable params: 1189632
2025-07-14 22:02:15,368 [ranpac.py] => Learning on 0-100
87,065,089 total parameters.
1,266,433 training parameters.
backbone.blocks.0.adaptmlp.down_proj.weight 49152
backbone.blocks.0.adaptmlp.down_proj.bias 64
backbone.blocks.0.adaptmlp.up_proj.weight 49152
backbone.blocks.0.adaptmlp.up_proj.bias 768
backbone.blocks.1.adaptmlp.down_proj.weight 49152
backbone.blocks.1.adaptmlp.down_proj.bias 64
backbone.blocks.1.adaptmlp.up_proj.weight 49152
backbone.blocks.1.adaptmlp.up_proj.bias 768
backbone.blocks.2.adaptmlp.down_proj.weight 49152
backbone.blocks.2.adaptmlp.down_proj.bias 64
backbone.blocks.2.adaptmlp.up_proj.weight 49152
backbone.blocks.2.adaptmlp.up_proj.bias 768
backbone.blocks.3.adaptmlp.down_proj.weight 49152
backbone.blocks.3.adaptmlp.down_proj.bias 64
backbone.blocks.3.adaptmlp.up_proj.weight 49152
backbone.blocks.3.adaptmlp.up_proj.bias 768
backbone.blocks.4.adaptmlp.down_proj.weight 49152
backbone.blocks.4.adaptmlp.down_proj.bias 64
backbone.blocks.4.adaptmlp.up_proj.weight 49152
backbone.blocks.4.adaptmlp.up_proj.bias 768
backbone.blocks.5.adaptmlp.down_proj.weight 49152
backbone.blocks.5.adaptmlp.down_proj.bias 64
backbone.blocks.5.adaptmlp.up_proj.weight 49152
backbone.blocks.5.adaptmlp.up_proj.bias 768
backbone.blocks.6.adaptmlp.down_proj.weight 49152
backbone.blocks.6.adaptmlp.down_proj.bias 64
backbone.blocks.6.adaptmlp.up_proj.weight 49152
backbone.blocks.6.adaptmlp.up_proj.bias 768
backbone.blocks.7.adaptmlp.down_proj.weight 49152
backbone.blocks.7.adaptmlp.down_proj.bias 64
backbone.blocks.7.adaptmlp.up_proj.weight 49152
backbone.blocks.7.adaptmlp.up_proj.bias 768
backbone.blocks.8.adaptmlp.down_proj.weight 49152
backbone.blocks.8.adaptmlp.down_proj.bias 64
backbone.blocks.8.adaptmlp.up_proj.weight 49152
backbone.blocks.8.adaptmlp.up_proj.bias 768
backbone.blocks.9.adaptmlp.down_proj.weight 49152
backbone.blocks.9.adaptmlp.down_proj.bias 64
backbone.blocks.9.adaptmlp.up_proj.weight 49152
backbone.blocks.9.adaptmlp.up_proj.bias 768
backbone.blocks.10.adaptmlp.down_proj.weight 49152
backbone.blocks.10.adaptmlp.down_proj.bias 64
backbone.blocks.10.adaptmlp.up_proj.weight 49152
backbone.blocks.10.adaptmlp.up_proj.bias 768
backbone.blocks.11.adaptmlp.down_proj.weight 49152
backbone.blocks.11.adaptmlp.down_proj.bias 64
backbone.blocks.11.adaptmlp.up_proj.weight 49152
backbone.blocks.11.adaptmlp.up_proj.bias 768
fc.weight 76800
fc.sigma 1

  0%|          | 0/1 [00:00<?, ?it/s]
Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84:   0%|          | 0/1 [01:19<?, ?it/s]
Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84: 100%|██████████| 1/1 [01:19<00:00, 79.87s/it]
Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84: 100%|██████████| 1/1 [01:19<00:00, 79.87s/it]
2025-07-14 22:03:35,297 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
selected lambda =  1000000.0
2025-07-14 22:04:27,549 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-14 22:04:27,986 [ranpac.py] => [Dynamic-K] Base similarity range: [6.6863, 10.5749]
2025-07-14 22:04:27,986 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-14 22:04:27,986 [trainer.py] => No NME accuracy.
2025-07-14 22:04:27,987 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-14 22:04:27,987 [trainer.py] => CNN HM: [0.0]
2025-07-14 22:04:27,987 [trainer.py] => CNN top1 curve: [90.43]
Average Accuracy (CNN): 90.43
2025-07-14 22:04:27,987 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-14 22:04:27,987 [trainer.py] => All params: 87988289
2025-07-14 22:04:27,988 [trainer.py] => Trainable params: 1189633
2025-07-14 22:04:28,059 [ranpac.py] => Learning on 100-110
2025-07-14 22:05:10,018 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 22:05:10,021 [ranpac.py] => [Dynamic-K] Computed K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-14 22:05:10,022 [ranpac.py] => [KNN] task 1, dynamic K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-14 22:05:10,667 [ranpac.py] => [KNN] task 1, weight sparsity: 0.891, distance_metric: mahalanobis
/home/<USER>/workdir/FSCIL-Calibration-main/models/ranpac.py:137: RuntimeWarning: covariance is not positive-semidefinite.
  xx  = np.random.multivariate_normal(self.proto_list[class_index], self.cov_mats[class_index], 800)
slurmstepd: error: *** JOB 859432 ON gpu59 CANCELLED AT 2025-07-14T22:05:31 ***
